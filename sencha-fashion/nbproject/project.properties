annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
application.title=sencha-fashion
application.vendor=kevin
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
# This directory is removed when the project is cleaned:
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
# Only compile against the classpath explicitly listed here:
build.sysclasspath=ignore
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
# Uncomment to specify the preferred debugger connection transport:
#debug.transport=dt_socket
debug.classpath=${run.classpath}
debug.test.classpath=${run.test.classpath}
# Files in build.classes.dir which should be excluded from distribution jar
dist.archive.excludes=
# This directory is removed when the project is cleaned:
dist.dir=dist
dist.jar=${dist.dir}/sencha-fashion.jar
dist.javadoc.dir=${dist.dir}/javadoc
endorsed.classpath=
excludes=
includes=**
jar.compress=false
junit.classpath=../lib/junit-4.11.jar\:../lib/hamcrest-core-1.3.jar
javac.classpath=${ivy.classpath}\:${junit.classpath}\:${reference.sencha-command.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.deprecation=false
javac.processorpath=${javac.classpath}
javac.source=1.8
javac.target=1.8
javac.test.classpath=${javac.classpath}\:${junit.classpath}\:${build.classes.dir}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=true
platform.active=default_platform
project.sencha-command=../sencha-command
reference.sencha-command.jar=${project.sencha-command}/dist/SenchaCmd/sencha.jar
run.classpath=${javac.classpath}\:${build.classes.dir}
# Space-separated list of JVM arguments used when running the project.
# You may also define separate properties like run-sys-prop.name=value instead of -Dname=value.
# To set system properties for unit tests define test-sys-prop.name=value:
run.jvmargs=
run.test.classpath=${dist.jar}\:${reference.sencha-command.jar}\:${junit.classpath}\:${build.test.classes.dir}
source.encoding=UTF-8
src.dir=src
test.src.dir=test

ivy.classpath=lib/ant-1.8.4.jar\:lib/ant-launcher-1.8.4.jar\:lib/jakarta.servlet-api-6.0.0.jar\:lib/jetty-alpn-client-12.0.15.jar\:lib/jetty-client-12.0.15.jar\:lib/jetty-ee-12.0.15.jar\:lib/jetty-ee10-servlet-12.0.15.jar\:lib/jetty-ee10-webapp-12.0.15.jar\:lib/jetty-http-12.0.15.jar\:lib/jetty-io-12.0.15.jar\:lib/jetty-proxy-12.0.15.jar\:lib/jetty-security-12.0.15.jar\:lib/jetty-server-12.0.15.jar\:lib/jetty-session-12.0.15.jar\:lib/jetty-util-12.0.15.jar\:lib/jetty-xml-12.0.15.jar\:lib/slf4j-api-1.6.6.jar\:lib/slf4j-api-2.0.16.jar
