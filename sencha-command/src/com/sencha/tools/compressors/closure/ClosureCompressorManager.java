/*
 * Copyright (c) 2012-2023. Sencha Inc.
 */

package com.sencha.tools.compressors.closure;

import com.sencha.util.Locator;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Manages Closure Compiler installations
 */
public class ClosureCompressorManager {
    
    /**
     * Gets the directory where Closure Compiler JAR files are stored
     * 
     * @return Path to the Closure Compiler directory
     */
    public static Path getClosureCompilerDirectory() {
        // Store in the Sencha Cmd directory under "closure"
        return Paths.get(Locator.getBaseDir(), "closure");
    }
    
    /**
     * Gets the path to the default Closure Compiler JAR
     * 
     * @return Path to the default Closure Compiler JAR
     */
    public static Path getDefaultClosureCompilerJar() {
        return getClosureCompilerDirectory().resolve("closure-compiler.jar");
    }
    
    /**
     * Gets the path to a specific version of the Closure Compiler JAR
     * 
     * @param version The version to get
     * @return Path to the specified Closure Compiler JAR
     */
    public static Path getClosureCompilerJar(String version) {
        return getClosureCompilerDirectory().resolve("closure-compiler-" + version + ".jar");
    }
}