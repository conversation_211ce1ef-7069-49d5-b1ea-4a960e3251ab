/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.command;

import com.phloc.commons.io.file.filter.FilenameFilterStartsWith;
import com.sencha.cli.*;
import com.sencha.cli.annotations.ConfigKey;
import com.sencha.cli.annotations.Doc;
import com.sencha.cli.annotations.Private;
import com.sencha.cli.annotations.Required;
import com.sencha.command.ant.AntCommand;
import com.sencha.command.app.AppCommands;
import com.sencha.command.audit.AuditCommand;
import com.sencha.command.build.BuildCommandRedirector;
import com.sencha.command.build.JsbCommand;
import com.sencha.command.cert.CertCommands;
import com.sencha.command.compile.CompileCommands;
import com.sencha.command.doxi.DoxiCommands;
import com.sencha.command.environment.BuildEnvironment;
import com.sencha.command.environment.FrameworkEnvironment;
import com.sencha.command.environment.IncompatibleException;
import com.sencha.command.extensions.Extension;
import com.sencha.command.extensions.ExtensionLoader;
import com.sencha.command.filesystem.FileSystemCommands;
import com.sencha.command.filesystem.WebCommands;
import com.sencha.command.framework.FrameworkCommands;
import com.sencha.command.generator.GeneratorCommands;
import com.sencha.command.id.IdCommands;
import com.sencha.command.id.LoginCommand;
import com.sencha.command.jasmine.PhantomCommand;
import com.sencha.command.js.JSCommand;
import com.sencha.command.manifest.ManifestCommands;
import com.sencha.command.pkg.PackageCommands;
import com.sencha.command.pkg.RepositoryCommands;
import com.sencha.command.schema.SchemaCommands;
import com.sencha.command.closure.ClosureCommands;
import com.sencha.command.space.SpaceCommands;
import com.sencha.command.template.TemplateCommands;
import com.sencha.command.theme.ThemeCommands;
import com.sencha.command.workspace.WorkspaceCommands;
import com.sencha.exceptions.BasicException;
import com.sencha.exceptions.ExAnt;
import com.sencha.exceptions.ExInterrupted;
import com.sencha.exceptions.ExState;
import com.sencha.logging.SenchaConsoleFormatter;
import com.sencha.logging.SenchaLogManager;
import com.sencha.logging.TemplateFormatter;
import com.sencha.tools.pkg.LocalRepository;
import com.sencha.util.*;
import org.slf4j.Logger;

import java.io.*;
import java.nio.channels.ClosedByInterruptException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.sencha.util.CollectionUtil.wrap;
import static com.sencha.util.ConsoleUtil.echo;
import static com.sencha.util.StringUtil.formatString;
import static com.sencha.util.StringUtil.isNullOrEmpty;

public class Sencha extends Commands {
    public Sencha() {
        this(true);
    }

    public Sencha (boolean doLoadConfigs) {
        setCommandName("sencha");

        if (doLoadConfigs) {
            loadBuildEnvironment();
            getBuildEnvironment().syncSystemProperties();
        }
    }

    @Override
    public void dispatch (Arguments arguments) {
        long start = System.currentTimeMillis();

        super.dispatch(arguments);

        if (getTime()) {
            long millis = System.currentTimeMillis() - start;
            long seconds = millis / 1000;

            if(!_noLogo) {
                _logger.info("");
                _logger.info(StringUtil.formatString("*** Time %d:%02d:%02d",
                             seconds / 3600, (seconds / 60) % 60, seconds % 60));
            }
        }
    }

    /**
     * @param args the command line arguments
     */
    public static void main (String[] args) {
        // a variable to allow semi-quiet output after we've gotten to the point
        // of dispatching commands
        File redirectFile = null;

        Arguments arguments = (args.length > 0) ? new Arguments(args) : new Arguments("help");
        System.setProperty("polyglot.js.nashorn-compat", "true");
        System.setProperty("polyglot.engine.WarnInterpreterOnly", "false");

        Sencha sencha = null;
        int exitcode = 0;
        try {
            SenchaLogManager.initDefaultLogging();

            redirectFile = new File("sencha.redirect");
            if (redirectFile.exists()) {
                redirectFile.delete();
            }

            sencha = new Sencha(false);

            String firstArg = arguments.peek(0);
            if(firstArg.equals("-debug") || firstArg.equals("-d")) {
                sencha.setDebug(true);
                arguments.pull();
            } else if(firstArg.equals("-trace") || firstArg.equals("-t")) {
                sencha.setTrace(true);
                arguments.pull();
            } else if(firstArg.equals("-nologo")) {
                sencha.setNologo(true);
                arguments.pull();
            }

            if (!sencha._noLogo) {
                Configuration cfg = new Configuration();
                cfg.load(new File(Locator.getBaseFile(), "sencha.cfg"));
                Ansi.echo(Ansi.B("Sencha Cmd v" + cfg.get("cmd.version")));
            }

            sencha.loadBuildEnvironment();
            sencha.configure(arguments);

            if (!sencha._noLogo) {
                if (sencha.getBuildEnvironment().getCmdEnvironment().isUpdateAvailable()) {
                    _logger.warn("Sencha Cmd update is available (updating is recommended)");
                }
            }

            // from here, we allow the system logging level to control whether stack
            // traces are displayed for uncaught exceptions in the catch block below
            _doShowTrace = false;

            LicenseUtil.update(sencha._environment,
                    sencha.getVersion(),
                    sencha._customConfigs, arguments);

            sencha.dispatch(arguments);
            echo("");
        } catch (IncompatibleException ex) {
            File matchPath = ex.getMatchPath();
            if(matchPath != null) {
                String matchAbsPath = PathUtil.normalizePathCharacters(matchPath.getAbsolutePath());
                _logger.debug("match found, redirecting to {} ...", matchAbsPath);
                if(redirectFile != null) {
                    FileUtil.writeFile(redirectFile, "redirect=" + matchAbsPath);
                } else {
                    _errStream.println(matchAbsPath);
                }
                exitcode = 42;
            } else {
                _logger.error(ex.getMessage());
                exitcode = 321;
            }
        } catch (Exception ex) {
            if (_logger.isDebugEnabled() || _logger.isTraceEnabled()) {
                _logger.error(BasicException.stringify(ex));
            } else if (_doShowTrace ||
                ex instanceof ExAnt ||
                (ex instanceof RuntimeException && !(ex instanceof BasicException))) {
                echo("");
                _logger.error(ex.getMessage());
                logCrashToFile(ex, sencha, args);
            } else if (!(ex instanceof InterruptedException || ex instanceof ExInterrupted || ex instanceof ClosedByInterruptException)) {
                _logger.error(ex.getMessage());
            }

            if (sencha != null) {
                BuildEnvironment be = sencha.getBuildEnvironment();
                if (be != null) {
                    be.verifyCmdVersionCompatibility();
                }
            }
            exitcode = 321;
        } finally {
            if (exitcode == 0 && sencha.isStrict()) {
                if (SenchaLogManager.getWarningCount() + SenchaLogManager.getErrorCount() > 0) {
                    exitcode = 22;
                    _logger.warn("Exiting with code {}, {} warnings and {} errors", new Object[]{
                        exitcode,
                        SenchaLogManager.getWarningCount(),
                        SenchaLogManager.getErrorCount()
                    });
                }
            }
        }

        // do not call System.exit with 0 here, because if there are foreground thread running
        // like jetty, they may need to prevent the jvm from exiting. SDKTOOLS-1007
        if(exitcode > 0) {
            System.exit(exitcode);
        }
    }

    //-------------------------------------------------------------------------
    // Commands

    public AntCommand createAnt () {
        return new AntCommand();
    }

    public BuildCommandRedirector createBuild () {
        return new BuildCommandRedirector();
    }

    public JsbCommand createLegacybuild () {
        return new JsbCommand();
    }

    public CompileCommands createCompile () {
        return new CompileCommands();
    }

    public ConfigCommand createConfig () {
        return new ConfigCommand();
    }

    @Private
    public DoxiCommands createDoxi () {
        return new DoxiCommands();
    }

    public GeneratorCommands createGenerate () {
        return new GeneratorCommands();
    }

    public HelpCommand createHelp () {
        return new HelpCommand();
    }

    public RepositoryCommands createRepository () {
        return new RepositoryCommands();
    }

    public ThemeCommands createTheme() {
        return new ThemeCommands();
    }

    public DiagCommand createDiag () {
        return new DiagCommand();
    }

    public AppCommands createApp() {
        return new AppCommands();
    }

    public FileSystemCommands createFs() {
        return new FileSystemCommands();
    }

    public JSCommand createJs () {
        return new JSCommand();
    }

    public ManifestCommands createManifest() {
        return new ManifestCommands();
    }

    public PhantomCommand createPhantom() {
        return new PhantomCommand();
    }

    public SchemaCommands createSchema () {
        return new SchemaCommands();
    }

    public TemplateCommands createTemplate () {
        return new TemplateCommands();
    }

    public WebCommands createWeb () {
        return new WebCommands();
    }

    public WhichCommand createWhich () {
        return new WhichCommand();
    }

    public CertCommands createCert () {
        return new CertCommands();
    }

    public PackageCommands createPackage () {
        return new PackageCommands();
    }

    public UpgradeCommand createUpgrade () {
        return new UpgradeCommand();
    }

    public LoginCommand createLogin () { return new LoginCommand(); }

    public IdCommands createId () { return new IdCommands(); }

    @Private
    public SpaceCommands createSpace() { return new SpaceCommands(); }

    public SpaceCommands createManager() { return new SpaceCommands(); }

    public SwitchCommand createSwitch () { return new SwitchCommand(); }

    public FrameworkCommands createFramework() { return new FrameworkCommands() ; }

    public AuditCommand createAudit() { return new AuditCommand(); }

    public WorkspaceCommands createWorkspace() { return new WorkspaceCommands(); }

    public ClosureCommands createClosure() {
        return new ClosureCommands();
    }

    //-------------------------------------------------------------------------
    // Properties

    public Version getVersion () {
        try {
            return new Version(getConfiguration().get("cmd.version").toString());
        } catch (Exception ex) {
            Configuration cfg = new Configuration();
            cfg.load(new File(Locator.getBaseFile(), "sencha.cfg"));

            return new Version(cfg.get("cmd.version").toString());
        }
    }

    public BuildEnvironment getBuildEnvironment() {
        return _environment;
    }

    @Doc("Sets log level to higher verbosity")
    public void setDebug (boolean debug) {
        if (debug) {
            SenchaLogManager.setLogLevelDebug();
        }
    }

    @Doc("Sets log level to default")
    public void setInfo(boolean enable) {
        if(enable) {
            SenchaLogManager.setLogLevelInfo();
            _doShowTrace = true;
        }
    }

    @Doc("Enable beta package repositories")
    public void setBeta(boolean enable) {
        _beta = enable;
        getCustomConfiguration().set("enable.beta.repo.remote", _beta);
        loadBuildEnvironment();
    }

    public boolean getBeta() {
        return _beta;
    }

    public boolean getDebug() {
        return SenchaLogManager.isLogLevelDebug();
    }

    @Doc("Suppress the initial Sencha Cmd version display")
    public void setNologo (boolean b) {
        _noLogo = b;
    }

    public void setPause (boolean enable) {
        _paused = enable;

        if (!_paused) {
            return;
        }

        System.out.print("Sencha Cmd has been paused, would you like to resume? [true/false]: ");

        Scanner sc = new Scanner(System.in);
        String next;
        while (_paused) {
            try {
                 next = sc.next().trim();

                _paused = !Boolean.parseBoolean(next);

                Thread.sleep(1000);
            } catch (InterruptedException e) {
            } catch (ExInterrupted e) {
            }
        }
    }

    @Doc("enables plain logging output (no highlighting)")
    public void setPlain (boolean enable) {
        // enabling simple should disable the ansi codes (inverted)
        SenchaConsoleFormatter.EnableHighlights = !enable;
        if (enable) {
            SenchaLogManager.setFormatter(TemplateFormatter.class);
        }
    }

    public boolean getPlain() {
        return !SenchaConsoleFormatter.EnableHighlights;
    }

    @Doc("Sets log level to warnings and errors only")
    public void setQuiet (boolean debug) {
        if (debug) {
            SenchaLogManager.setLogLevelWarn();
        }
    }

    public boolean getQuiet() {
        return SenchaLogManager.isLogLevelQuiet();
    }

    @Private
    @Doc("Sets log level to the highest verbosity")
    public void setTrace (boolean enable) {
        if (enable) {
            SenchaLogManager.setLogLevelTrace();
        }
    }

    public boolean getTrace() {
        return SenchaLogManager.isLogLevelTrace();
    }

    @Doc("The location of the SDK to use for non-app commands")
    public void setSdkPath(String path) {
        setCwd(path);
        if(!getBuildEnvironment().isFrameworkEnvironment()) {
            _logger.error(getFrameworkNotFoundError());
            throw new ExState("Not a framework directory : {0}", path);
        }
    }

    @Doc("Sets the directory from which commands should execute")
    public void setCwd(String path) {
        applyCurrentWorkingDirectory(path, true);
    }

    @Doc("Sets the directory to store crash logs. If unspecified, logs are written to the process cwd.")
    public void setCrashLogDir(String dir) {
        _crashLogDir = dir;
    }

    public boolean getTime () {
        return _time;
    }

    @Doc("Display the execution time after executing all commands")
    public void setTime (boolean b) {
        _time = b;
    }

    public File resolveFile (String path) {
        File file;

        if (PathUtil.isAbsolute(path)) {
            file = new File(path);
        } else {
            File base = new File(StringUtil.defaultString(_cwd, "."));
            file = new File(base, path);
        }

        return PathUtil.getCanonicalFile(file);
    }

    public final void loadBuildEnvironment() {
        String cwd = _cwd;

        Configuration curr = getConfiguration();
        Configuration custom = getCustomConfiguration();

        // if unspecified working directory, check some other
        // places a directory specification might've been stored
        if(StringUtil.isNullOrEmpty(cwd)) {
            if(custom != null && custom.has(ENV_LOAD_DIR)) {
                cwd = custom.get(ENV_LOAD_DIR).toString();
            } else if(curr != null && curr.has(ENV_LOAD_DIR)) {
                cwd = curr.get(ENV_LOAD_DIR).toString();
            } else {
                cwd = ".";
            }
        }

        File path = PathUtil.getCanonicalFile(cwd);
        _logger.debug("Loading configuration from {}", path);
        setBuildEnvironment(BuildEnvironment.load(custom, path));
    }

    public void applyCurrentWorkingDirectory(String path, boolean reload) {
        if (!isNullOrEmpty(path)) {
            _cwd = path;
            if(reload) {
                loadBuildEnvironment();
            }
        }
    }

    public void setBuildEnvironment(BuildEnvironment be) {
        if (be instanceof FrameworkEnvironment && be.getPackageEnvironment() != null) {
            _environment = be.getPackageEnvironment();
        } else {
            _environment = be;
        }

        Configuration newConfig = _environment.getConfig();
        Configuration curConfig = getConfiguration();

        if (curConfig != null) {
            newConfig.setNewProperties(curConfig);
        }

        super.setConfiguration(newConfig);
    }

    @Override
    protected void extendCommands (Commands commands) {
        for (ServiceLoader<CommandProvider> loader : getCommandProviders()) {
            for (CommandProvider provider : loader) {
                provider.extendCommands(commands);
            }
        }

        for (Extension ext : getExtensionLoader().getExtensions()) {
            ext.extendCommands(commands, getCustomConfiguration());
        }
    }

    @Override
    public void setConfiguration(Configuration config) {
        config.setNewProperties(_customConfigs);

        super.setConfiguration(config);

        if (config.has(ENV_LOAD_DIR)) {
           setCwd(config.get(ENV_LOAD_DIR).toString());
        }
    }

    public Configuration getCustomConfiguration() {
        return _customConfigs;
    }

    //-------------------------------------------------------------------------

    @Private
    @Required
    @ConfigKey("repo.local.dir")
    @Doc("Sets the folder of the local package repository")
    public void setRepositoryDir (File repoDir) {
        _repoDir = PathUtil.getCanonicalFile(repoDir);
        // customizations to repo.dir need to be placed into the
        // custom configuration space to allow them to be passed via
        // the 'sencha ant' command.  However, LocalRepository initialization
        // needs to have the current full configuration instance overridden
        // appropriately, and it will already have beeen created
        // with the current custom set, so the change must be applied ot both
        // configuration instances here.
        getConfiguration().set("repo.local.dir", _repoDir);
        getCustomConfiguration().set("repo.local.dir", _repoDir);
        _repo = null;
    }

    public LocalRepository getRepo (boolean mustBeInitialized) {
        if (_repo == null) {
            _repo = new LocalRepository(_repoDir, getConfiguration());
            _logger.debug("Using local repository {}", _repo.getBaseDir());
        }

        if (mustBeInitialized && !_repo.isInitialized()) {
            _repo.init();
        }

        _repo.getRemoteManager().setEnableBeta(_beta);
        return _repo;
    }

    public LocalRepository getRepo () {
        return getRepo(true);
    }

    //-------------------------------------------------------------------------
    // Private

    public static String getFrameworkNotFoundError() {
        return _frameworkNotFoundErr;
    }

    private static final String _frameworkNotFoundErr =
        "Unable to locate supported Framework.\n\n" +
        "      Please ensure this command was executed from a supported framework\n" +
        "      directory or that a framework directory was specified via the -sdk switch.\n" +
        "      For example:\n\n" +
        "          sencha -sdk /path/to/framework-dir generate app AppName path/to/app\n";

    private static void logCrashToFile (Exception ex, Sencha sencha, String[] args) {
        String path = (sencha._crashLogDir == null) ? "." : sencha._crashLogDir;
        String fileName = "sencha-error-" + new SimpleDateFormat("YYYYMMdd").format(new Date()) + ".log";

        File logFile = null;
        PrintWriter crashLog = null;
        try {
            logFile = new File(path + File.separator + fileName);

            if (logFile.isFile()) {
                // Check if the file already exists, if so we need to sufix the name
                // with -1, -2, etc.
                File[] logs = new File(path).listFiles(new FilenameFilterStartsWith("sencha-error-"));
                fileName = fileName.replace(".log", "-" + logs.length + ".log");
                logFile = new File(path + File.separator + fileName);
            }

            crashLog = new PrintWriter(new FileWriter(logFile));

            crashLog.println("Sencha Cmd " + sencha.getVersion() + " - Crash report");
            crashLog.println("================================================");
            crashLog.println("An error occurred while executing the following command: " +
                             StringUtil.join(args, " ") + "\n");

            if (sencha != null) {
                crashLog.println("Diagnostic information:");
                crashLog.println("=======================");
                // When the original exception was thrown while trying to load the build environment
                // the following lines would most likely throw as well, so let's wrap them.
                try {
                    BuildEnvironment be = sencha.getBuildEnvironment();
                    Configuration config = be.getConfig();
                    List<String> keys = wrap(config.getData().keySet()).doSort(
                            new Comparator<String>(){
                                @Override public int compare (String o1, String o2) {
                                    return o1.compareTo(o2);
                                }
                            });

                    for (String prop : keys) {
                        crashLog.println(formatString("%40s : %-50s", prop, config.get(prop)));
                    }
                } catch (Exception someEx) {
                    crashLog.println("Error while loading build environment: " + ex.getMessage());
                    crashLog.println();
                    crashLog.println(BasicException.stringify(someEx));
                }
                crashLog.println();
            }

            crashLog.println("Exception information:");
            crashLog.println("=======================");
            crashLog.println(BasicException.stringify(ex));

            _logger.error("A log is available in the file \"" + logFile.getCanonicalPath() + "\"");
        } catch (IOException ignored) {
            _logger.error(BasicException.stringify(ex));
        } finally {
            if (crashLog != null) {
                crashLog.close();
            }
        }
    }

    public boolean isStrict () {
        return _strict;
    }

    @Doc("Treats warnings as errors, exiting with error if any warnings are present")
    public void setStrict (boolean strict) {
        _strict = strict;
    }

    private static class PrintStreamWrapper extends PrintStream {
        public PrintStreamWrapper(OutputStream out) {
            super(out);
        }

        @Override
        public void close() {
            // ignore
        }
    }

    private static final PrintStream _errStream;

    static {
        _errStream = System.err;

        System.setErr(new PrintStreamWrapper(_errStream));
    }

    public static Collection<ServiceLoader<CommandProvider>> getCommandProviders() {
        if(_providers == null) {
            _providers = getExtensionLoader().getCommandProviders();
        }
        return _providers;
    }

    public static ExtensionLoader getExtensionLoader() {
        return _loader;
    }

    private static final ExtensionLoader _loader =
            new ExtensionLoader(Locator.getExtensionsDir());

    private static Collection<ServiceLoader<CommandProvider>> _providers;

    private static final Logger _logger = SenchaLogManager.getLogger();

    protected static final String ENV_LOAD_DIR = "buildenvironment.load.dir";

    private boolean _noLogo = false;
    private BuildEnvironment _environment;
    private boolean _paused;
    private final Configuration _customConfigs = new Configuration();
    private String _cwd; // current working directory
    private String _crashLogDir; // directory to store crash logs
    private LocalRepository _repo;
    private File _repoDir;
    private boolean _time;
    private boolean _strict = false;
    private boolean _beta = false;
    private static boolean _doShowTrace = true;
}
