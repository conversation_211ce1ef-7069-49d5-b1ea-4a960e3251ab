/*
 * Copyright (c) 2012-2023. Sencha Inc.
 */

package com.sencha.command.closure;

import com.sencha.cli.annotations.Doc;
import com.sencha.command.BaseSenchaCommands;
import com.sencha.command.BaseSenchaCommand;
import com.sencha.cli.annotations.ConfigKey;
import com.sencha.cli.annotations.Private;
import com.sencha.tools.compressors.closure.ClosureCompressorManager;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * Commands for managing Google Closure Compiler installations
 */
@Doc("Manages Google Closure Compiler installations")
public class ClosureCommands extends BaseSenchaCommands {

    @Doc("Installs a specific version of Google Closure Compiler")
    public class InstallCommand extends BaseSenchaCommand {
        private String _version;
        private String _destination;
        private boolean _force;

        @Doc("The version of Closure Compiler to install (e.g., 'v20230802')")
        public void setVersion(String version) {
            _version = version;
        }

        public String getVersion() {
            return _version;
        }

        @Doc("The destination directory to install Closure Compiler to (defaults to Sencha Cmd's closure directory)")
        public void setDestination(String destination) {
            _destination = destination;
        }

        public String getDestination() {
            return _destination;
        }

        @Doc("Force installation even if the version is already installed")
        public void setForce(boolean force) {
            _force = force;
        }

        public boolean getForce() {
            return _force;
        }

        public void execute() throws IOException {
            if (_version == null) {
                _version = "latest";
                System.out.println("No version specified, using latest release");
            }

            // Determine destination directory
            Path destDir;
            if (_destination != null) {
                destDir = Paths.get(_destination);
            } else {
                destDir = ClosureCompressorManager.getClosureCompilerDirectory();
            }

            // Create directory if it doesn't exist
            Files.createDirectories(destDir);

            // Construct download URL
            String urlStr;
            if ("latest".equals(_version)) {
                urlStr = "https://repo1.maven.org/maven2/com/google/javascript/closure-compiler/latest/closure-compiler-latest.jar";
            } else {
                // If version doesn't start with 'v', add it
                String versionStr = _version.startsWith("v") ? _version : "v" + _version;
                urlStr = "https://repo1.maven.org/maven2/com/google/javascript/closure-compiler/" + 
                         versionStr + "/closure-compiler-" + versionStr + ".jar";
            }

            // Determine target file
            Path targetFile = destDir.resolve("closure-compiler-" + _version + ".jar");
            
            // Check if already exists
            if (Files.exists(targetFile) && !_force) {
                System.out.println("Closure Compiler version " + _version + " is already installed at " + targetFile);
                System.out.println("Use --force to reinstall");
                return;
            }

            System.out.println("Downloading Closure Compiler " + _version + " from " + urlStr);
            
            try {
                // Download the file
                URL url = new URL(urlStr);
                Files.copy(url.openStream(), targetFile, StandardCopyOption.REPLACE_EXISTING);
                
                // Create a symlink or copy for the default version if requested
                if (_force || !Files.exists(destDir.resolve("closure-compiler.jar"))) {
                    Path defaultLink = destDir.resolve("closure-compiler.jar");
                    try {
                        Files.deleteIfExists(defaultLink);
                        Files.copy(targetFile, defaultLink);
                        System.out.println("Set as default Closure Compiler version");
                    } catch (IOException e) {
                        System.err.println("Warning: Could not set as default version: " + e.getMessage());
                    }
                }
                
                System.out.println("Successfully installed Closure Compiler " + _version + " to " + targetFile);
            } catch (IOException e) {
                System.err.println("Error downloading Closure Compiler: " + e.getMessage());
                throw e;
            }
        }
    }

    @Doc("Lists installed versions of Google Closure Compiler")
    public class ListCommand extends BaseSenchaCommand {
        private String _directory;

        @Doc("The directory to search for Closure Compiler installations")
        public void setDirectory(String directory) {
            _directory = directory;
        }

        public String getDirectory() {
            return _directory;
        }

        public void execute() throws IOException {
            // Determine search directory
            Path searchDir;
            if (_directory != null) {
                searchDir = Paths.get(_directory);
            } else {
                searchDir = ClosureCompressorManager.getClosureCompilerDirectory();
            }

            if (!Files.exists(searchDir)) {
                System.out.println("No Closure Compiler installations found in " + searchDir);
                return;
            }

            System.out.println("Installed Closure Compiler versions in " + searchDir + ":");
            
            // Find all closure-compiler-*.jar files
            File[] files = searchDir.toFile().listFiles((dir, name) -> 
                name.startsWith("closure-compiler-") && name.endsWith(".jar"));
            
            if (files == null || files.length == 0) {
                System.out.println("No Closure Compiler installations found");
                return;
            }

            // Determine default version
            Path defaultJar = searchDir.resolve("closure-compiler.jar");
            String defaultVersion = null;
            
            if (Files.exists(defaultJar)) {
                try {
                    File canonicalDefault = defaultJar.toFile().getCanonicalFile();
                    for (File file : files) {
                        if (file.getCanonicalFile().equals(canonicalDefault)) {
                            defaultVersion = file.getName().replace("closure-compiler-", "").replace(".jar", "");
                            break;
                        }
                    }
                } catch (IOException e) {
                    // Ignore, just won't show default
                }
            }

            // Print versions
            for (File file : files) {
                String version = file.getName().replace("closure-compiler-", "").replace(".jar", "");
                if (version.equals(defaultVersion)) {
                    System.out.println("* " + version + " (default)");
                } else {
                    System.out.println("  " + version);
                }
            }
        }
    }

    @Doc("Sets the default version of Google Closure Compiler to use")
    public class UseCommand extends BaseSenchaCommand {
        private String _version;
        private String _directory;

        @Doc("The version of Closure Compiler to set as default")
        public void setVersion(String version) {
            _version = version;
        }

        public String getVersion() {
            return _version;
        }

        @Doc("The directory containing Closure Compiler installations")
        public void setDirectory(String directory) {
            _directory = directory;
        }

        public String getDirectory() {
            return _directory;
        }

        public void execute() throws IOException {
            if (_version == null) {
                System.err.println("Error: Version must be specified");
                return;
            }

            // Determine directory
            Path dir;
            if (_directory != null) {
                dir = Paths.get(_directory);
            } else {
                dir = ClosureCompressorManager.getClosureCompilerDirectory();
            }

            // Find the specified version
            String filename = "closure-compiler-" + _version + ".jar";
            Path versionPath = dir.resolve(filename);
            
            if (!Files.exists(versionPath)) {
                System.err.println("Error: Version " + _version + " not found at " + versionPath);
                System.err.println("Use 'sencha closure list' to see available versions");
                System.err.println("or 'sencha closure install --version=" + _version + "' to install it");
                return;
            }

            // Set as default
            Path defaultPath = dir.resolve("closure-compiler.jar");
            Files.deleteIfExists(defaultPath);
            Files.copy(versionPath, defaultPath);
            
            System.out.println("Set Closure Compiler version " + _version + " as default");
        }
    }
}