# Closure Compiler Management

These commands help you manage Google Closure Compiler installations for use with Sencha Cmd.

## Installing a Specific Version

To install a specific version of the Closure Compiler:

    sencha closure install --version=v20230802

If no version is specified, the latest version will be installed:

    sencha closure install

## Listing Installed Versions

To see all installed versions of the Closure Compiler:

    sencha closure list

## Setting the Default Version

To set a specific installed version as the default:

    sencha closure use --version=v20230802

## Using a Specific Version in app.json

You can specify which version of the Closure Compiler to use in your app.json:

```json
"production": {
    "compressor": {
        "type": "closure",
        "version": "v20230802",
        "compilationLevel": "SIMPLE_OPTIMIZATIONS"
    }
}
```

This allows you to use different versions of the Closure Compiler for different projects.