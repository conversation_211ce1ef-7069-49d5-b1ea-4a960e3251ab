<!DOCTYPE html>
<html>
<head>
    <title>
        Sencha Cmd Release Notes
    </title>
    <style>
        body {
            color: #333;
            padding: 0 40px 40px;
            font-family: Helvetica, Arial, Geneva, sans-serif;
            font-size: 12px;
            line-height: normal;
            width: 800px;
            margin: 0 auto;
        }

        a#logo {
            background: transparent url(logo-sencha-sm.png) no-repeat scroll 0px 100%;
            display: block;
            height: 58px;
            margin: 0;
            padding: 0;
            width: 134px;

            overflow: hidden;
            text-indent: 150%;
            white-space: nowrap;
        }

        h1, h2, h3 {
            font-family: 'Helvetica Neue', Helvetica, Arial, Geneva, sans-serif;
            font-weight: 600;
            margin: 1em 0;
        }
        h1 { font-size: 28px; }
        h2 { font-size: 20px; }
        h3 { font-size: 16px; }
        ul li { padding: 2px 0; }
        .doc { color: blue; }
        .change { color: #196cb4; }
        .new { color: #6e7a32; }
        ul { padding: .1em 1.5em; }
        .notes { color: #777; }
        a { color: #0464BB; }
        a:hover { color: rgb(28, 65, 124); }

        .hilite {
            background-color: yellow;
            font-weight: bold;
        }
        pre {
            background: ghostWhite;
            border: 1px solid #ddd;
            font-family: Menlo, Monaco, Courier, monospace;
            overflow: auto;
            padding: 12px;
        }

        hr {
            color: #e0e0e0;
            background-color: #e0e0e0;
            height: 4px;
            border: none;
            outline: none;
        }

        .platform {
            display: inline-block;
        }
        .platforms {
            padding: 8px 16px 0 16px;
            background-color: #f8f8f8;
            width: 30em;
        }

        .installers {
            display: inline-block;
            padding: 0;
            margin: 0;
        }
        
        .installers a {
            padding-left: 10px;
        }
        
        .platforms > div,
        .linux > div {
            padding-bottom: 6px;
        }
        .current-platform {
            font-weight: 800;
            font-style: italic;
        }

        .alt-platform,
        .alt.collapsed {
            display: none;
        }
        .platform.alt-platform + br ,
        .platform.alt-platform + .installers + br {
            display: none;
        }

        .all-platforms .alt-platform,
        .alt {
            display: inline-block;
        }
        .all-platforms .platform.alt-platform + .installers + br,
        .all-platforms .platform.alt-platform + br {
            display: block;
        }

        .expandable {
            cursor: pointer;
            position: relative;
            padding-left: 18px;
        }

        .expandable:before {
            content: "-";
            font-family: monospace;
            font-weight: bold;
            font-size: 14px;
            vertical-align: middle;
            line-height: 14px;
            width: 14px;
            text-align: center;
            background-color: #ddd;
            color: gray;
            position: absolute;
            left: 0;
            top: 0;
        }

        .collapsed.expandable:before {
            content: "+";
        }
        
        .other .all-platforms-checkbox {
            display: none;
        }
        
        .options {
            float: right;
        }
        
        .platform.linux {
            padding-left: 4em;
            position: relative;
        }
        .platform.linux:before {
            position: absolute;
            top: 0;
            left: 0;
            content: "Linux:";
        }

        .disable-selection {
            -moz-user-select: none;
            -ms-user-select: none;
            -khtml-user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
        }
    </style>
    <script type="text/javascript">
        function onLoad () {
            var names = {
                mac: 'mac',
                win: 'win',
                linux: 'linux',
                other: 'other'
            };

            var ua = navigator.userAgent;
            var os = names[(ua.toLowerCase().match(/mac|win|linux/) || ['other'])[0]] || 'other';
            if (os === 'win' || os === 'linux') {
                var re64 = /x86_64|x86-64|Win64|x64;|amd64|AMD64|WOW64|x64_64/;
                if (re64.test(ua)) {
                    os += '64';
                } else {
                    os += '32';
                }
            }

            var m = (location.search || '').match(/\b(mac|win32|win64|linux32|linux64|other)\b/);
            if (m) {
                os = m[1];
            }
            //os = 'win32';
            //os = 'win64';
            //os = 'mac';
            //os = 'linux32';
            //os = 'linux64';
            //os = 'other';
            document.body.className = os;

            function indexOf (array, v) {
                for (var i = 0; i < array.length; ++i) {
                    if (array[i] === v) {
                        return i;
                    }
                }
                return -1;
            }
            function getClasses (el) {
                return el ? (el.className || '').split(' ') : [];
            }

            function addCls (el, cls) {
                var array = getClasses(el);
                if (indexOf(array, cls) < 0) {
                    array.push(cls);
                    el.className = array.join(' ');
                }
                return el;
            }
            function hasCls (el, cls) {
                var array = getClasses(el);
                return indexOf(array, cls) >= 0;
            }
            function removeCls (el, cls) {
                var array = getClasses(el);
                var i = indexOf(array, cls);
                
                if (i >= 0) {
                    array.splice(i, 1);
                    el.className = array.join(' ');
                }
                return el;
            }
            function toggleCls (el, cls, state) {
                if (typeof state !== 'boolean') {
                    state = !hasCls(el, cls);
                }
                (state ? addCls : removeCls)(el, cls);
            }

            function each (list, fn) {
                if (list) {
                    if (typeof list.length === 'number') {
                        for (var i = 0, n = list && list.length; i < n; ++i) {
                            if (false === fn(list[i], i)) {
                                break;
                            }
                        }
                    } else {
                        for (var i in list) {
                            if (false === fn(list[i], i)) {
                                break;
                            }
                        }
                    }
                }
            }

            each({
                '.win .jre32': 'Windows installer (including 32-bit JRE)',
                '.win .jre64': 'Windows installer (including 64-bit JRE)',
                '.win .nojre': 'Windows installer (no JRE included)',
                
                '.mac .jre': 'Mac OS X installer (including JRE)',
                '.mac .nojre': 'Mac OS X installer (no JRE included)',
                
                '.linux32 .sh': 'Linux 32-bit (i386) executable installer (no JRE included)',
                '.linux64 .sh': 'Linux 64-bit (amd64) executable installer (no JRE included)',

                '.linux32 .deb': 'Linux 32-bit (i386) Debian package',
                '.linux64 .deb': 'Linux 64-bit (amd64) Debian package',

                '.linux32 .rpm': 'Linux 32-bit (i386) RPM package',
                '.linux64 .rpm': 'Linux 64-bit (amd64) RPM package',
                
                '.win .zip': 'Windows archive (no installer)',
                '.mac .zip': 'Mac OS X archive (no installer)',
                '.linux32 .zip': 'Linux 32-bit (i386) archive (no installer)',
                '.linux64 .zip': 'Linux 64-bit (amd64) archive (no installer)'
            }, function (value, key) {
                each(document.querySelectorAll(key), function (el) {
                    el.title = value;
                });
            });

            function onKeyDown (e) {
                if (e.which === 13 || e.which === 32) {
                    e.target.onclick();
                }
            }

            if (os !== 'other') {
                each(document.querySelectorAll('.platform'), function (el) {
                    addCls(el, 'alt-platform');
                });

                each(document.querySelectorAll('.installers'), function (el) {
                    addCls(el, 'alt-platform');
                });
            }

            each(document.querySelectorAll('.expandable'), function (el) {
                el.setAttribute('tabIndex', 0);
                el.onkeydown = onKeyDown;
                el.title = 'Click to toggle display of all installers for this OS';
            });

            each(document.querySelectorAll('.platform.' + os), function (el) {
                removeCls(el, 'alt-platform');
                addCls(el, 'current-platform');
            });
            
            if (os === 'linux32' || os === 'linux64') {
                each(document.querySelectorAll('.platform.linux'), function (el) {
                    removeCls(el, 'alt-platform');
                });
            } else if (os === 'win32') {
                each(document.querySelectorAll('.jre64'), function (el) {
                    addCls(el, 'alt');
                });
            } else if (os === 'win64') {
                each(document.querySelectorAll('.jre32'), function (el) {
                    addCls(el, 'alt');
                });
            }

            each(document.querySelectorAll('.installers.' + os), function (el) {
                removeCls(el, 'alt-platform');
                addCls(el, 'current-platform');
            });

            each(document.querySelectorAll('.alt'), function (el) {
                addCls(el, 'collapsed');
            });

            window.toggleAlt = function (el) {
                toggleCls(el, 'collapsed');
                var collapsed = hasCls(el, 'collapsed');
                
                while (!hasCls(el, 'installers')) {
                    el = el.nextSibling;
                }
                each(el.querySelectorAll('.alt'), function (a) {
                    toggleCls(a, 'collapsed', collapsed);
                });
            };

            window.toggleOption = function (el, option) {
                var parent = el.parentNode.parentNode.parentNode;
                toggleCls(parent, option, el.checked);
            };
        }
    </script>
</head>
<body onload="onLoad()">
<p>
    <a href="http://www.sencha.com/" id="logo" name="logo">Sencha Cmd</a>
</p>

<div id="releases">
    <!-- ************************************************************************** -->
    <!--
    When a release is made edit the queries below to adjust for the next release.
    -->
    <div class="release">
        <h1>Release Notes for Sencha Cmd 7.9.0</h1>
        <div class="notes">
            Date: @@BUILD_DATE@@<br>
            Version Number: @@CMD_VERSION@@<br>
            <div class="platforms">
                <div class="disable-selection expandable collapsed platform win32 win64" onclick="toggleAlt(this)">Windows: </div>
                <div class="installers win win32 win64">
                    <a class="jre64" href="../@@CMD_VERSION@@/jre/SenchaCmd-@@CMD_VERSION@@-windows-64bit.zip">64-bit</a>
                    <a class="jre32" href="../@@CMD_VERSION@@/jre/SenchaCmd-@@CMD_VERSION@@-windows-32bit.zip">32-bit</a><span class="alt">
                    <a class="nojre" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-windows-no_jre.zip">no_jre</a>
                    <a class="zip" href="../@@CMD_VERSION@@/zip/SenchaCmd-@@CMD_VERSION@@-windows.zip">zip</a></span>
                </div>
                <br/>
                <div class="disable-selection expandable collapsed platform mac" onclick="toggleAlt(this)">Mac OS X:
                </div><div class="installers mac">
                    <a class="jre" href="../@@CMD_VERSION@@/jre/SenchaCmd-@@CMD_VERSION@@-osx.app.zip">app</a><span class="alt">
                    <a class="nojre" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-osx-no_jre.app.zip">no_jre</a>
                    <a class="zip" href="../@@CMD_VERSION@@/zip/SenchaCmd-@@CMD_VERSION@@-osx.zip">zip</a></span>
                </div>
                <br />
                <div class="disable-selection platform linux">
                    <div class="disable-selection expandable collapsed platform linux32" onclick="toggleAlt(this)">i386</div>
                    <div class="installers linux32">
                        <a class="sh" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-i386.sh.zip">sh</a><span class="alt">
                        <a class="deb" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-i386.deb">deb</a>
                        <a class="rpm" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-i386.rpm">rpm</a>
                        <a class="zip" href="../@@CMD_VERSION@@/zip/SenchaCmd-@@CMD_VERSION@@-linux-i386.zip">zip</a></span>
                    </div>
                    <br/>
                    <div class="disable-selection expandable collapsed platform linux64" onclick="toggleAlt(this)">amd64</div>
                    <div class="installers linux64">
                        <a class="sh" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-amd64.sh.zip">sh</a><span class="alt">
                        <a class="deb" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-amd64.deb">deb</a>
                        <a class="rpm" href="../@@CMD_VERSION@@/no-jre/SenchaCmd-@@CMD_VERSION@@-linux-amd64.rpm">rpm</a>
                        <a class="zip" href="../@@CMD_VERSION@@/zip/SenchaCmd-@@CMD_VERSION@@-linux-amd64.zip">zip</a></span>
                    </div>
                </div>
                <br/>
                <div class="options">
                    <label class="all-platforms-checkbox"><input type="checkbox" onclick="toggleOption(this, 'all-platforms')">All Platforms</label>
                </div>
                <div style="clear:both;"></div>
            </div>
        </div>

        <h2>New Features</h2>
        #jql
            project = SDKTOOLS and fixVersion in ("7.9.0") AND
            status in ("Pending Verification", "Pending Dev Verification", "Closed") AND
            type in ("Feature Request", Story) AND
            resolution = Fixed AND
            (Component is empty or Component != "Test") AND
            (labels is empty or labels != "skipReleaseNotes")
        #jql
        <h2>Bugs Fixed</h2> 
        #jql
            project = SDKTOOLS and fixVersion in ("7.9.0") AND
            status in ("Pending Verification", "Pending Dev Verification", "Closed") AND
            type in (Bug) AND
            resolution = Fixed AND
            (Component is empty or Component != "Test") AND
            (labels is empty or labels != "skipReleaseNotes")
        #jql
        <h2>Known Issues</h2>
        #jql
            project = SDKTOOLS and
            labels = "knownIssue" AND
            status in (Open, Assigned, "In Progress")
        #jql
    </div>
</div>
</body>
</html>
