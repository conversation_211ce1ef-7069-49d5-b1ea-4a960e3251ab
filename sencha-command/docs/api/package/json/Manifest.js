/**
 * @class Cmd.package.json.Manifest
 * @extend Cmd.codebase.json.Manifest
 * @singleton
 * This is not an actual class or singleton but rather the contents of `'package.json'`
 * which Sencha Cmd uses to describe a package.
 *
 * To maintain compatibility with Node.js, Sencha packages wrap these configs in a `"sencha"`
 * root property:
 *
 *      {
 *          ... Node.js properties ...
 *
 *          "sencha": {
 *              ... Sencha properties ...
 *          }
 *      }
 */

/**
 * @cfg {String/String[]} alternateName
 * One or more alternate package names. This is used to support old names for a package
 * when it has been renamed.
 */

/**
 * @cfg {Object} architect
 * TODO
 * @private
 */

/**
 * @cfg {Object} builds
 * This object's properties are "build profiles". You can add as many as you need to
 * produce optimized builds for devices, themes, locales or other criteria.
 */

/**
 * @cfg {String} compatVersion
 * The version that users can transparently update from without requiring code changes.
 *
 * In addition to the `version` property, packages can also indicate the degree to which
 * they are backward compatible using this property.
 */

/**
 * @cfg {String} [creator="anonymous"]
 * The author of the package.
 *
 * Required only if you are distributing this package through a Sencha Cmd repository,
 * in which case it should match the name you assign to your local package repository.
 */

/**
 * @cfg {Object} example
 * This config instructs the `sencha package build` command to also build one or more
 * example applications contained in `example.path`.
 *
 * @cfg {String} example.path
 * The path to the example(s) for this package. It is assumed that all sub-directories
 * of this location should be built using `sencha app build`.
 *
 * @cfg {String[]} example.apps
 * This optional array of strings are the names of apps that should be built using the
 * `sencha app build` command. This is used when only some of the subdirectories of
 * `examples.path` are buildable in this way.
 */

/**
 * @cfg {String} extend
 * For `"theme"` `type` packages only, this determines the styles and variables this theme
 * will inherit.
 */

/**
 * @cfg {Cmd.codebase.json.JS[]} js
 * One or more JavaScript assets to include either in the build or to load remotely.
 *
 *      "js": [{
 *          "path": "http://3rd.party.org/third-party.js",
 *          "remote": true
 *      }]
 */

/**
 * @cfg {Boolean} local
 * Indicates whether this is a locally developed package or downloaded form a repository.
 * Defaults to `true` on newly generated packages. This value is removed in the package's
 * build output and should not be removed manually.
 *
 * This flag was originally used by Sencha Cmd to avoid replacing local packages with
 * remote packages (when all packages were stored in the `./packages` directory).
 */

/**
 * @cfg {String} name
 * The name of the package. Should be a valid JavaScript identifier but can also use
 * hyphens. If a `name` is not specified in the `sencha` object, the `name` from the
 * outer (Node.js) level is assumed.
 */

/**
 * @cfg {String} namespace
 * The namespace of this package.
 *
 * As a general rule, all classes that belong to this package should be under this namespace
 * if multiple namespaces are part of this package, set this to "".
 */

/**
 * @cfg {Cmd.codebase.json.Output} output
 * The options used to control build output.
 */

/**
 * @cfg {Object/String[]} templates
 * An array that contains the paths to templates included in this package or an object
 * keyed by those paths. When using strings, the `$template.json` file is loaded from there.
 * Otherwise, the value is the {@link Cmd.template.json.Manifest template definition}.
 * @private
 */

/**
 * @cfg {"code"/"framework"/"locale"/"template"/"theme"} [type="code"]
 * The package's type. The role of `"theme"` packages is the most significantly different
 * as it controls whether CSS builds include all inherited styling. By default, `"code"`
 * packages only build their own CSS, though they may use mixins and variables from the
 * `theme`.
 */
