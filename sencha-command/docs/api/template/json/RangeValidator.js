/**
 * @class Cmd.template.json.RangeValidator
 * @extend Cmd.template.json.Validator
 * Validates that a value is in a particular range.
 *
 * Applies to parameters of type `"number"` or `"integer"`.
 * @private
 */

/**
 * @property {String} [type="range"]
 * @readonly
 */

/**
 * @cfg {String} bothMessage
 * The message to display when the validator rejects a value as larger than `max` or
 * less than `min` and both values are provided.
 */

/**
 * @cfg {Number} max
 * The maximum allowed value.
 */

/**
 * @cfg {String} maxMessage
 * The message to display when the validator rejects a value as larger than `max`.
 */

/**
 * @cfg {String} maxMessage
 * The message to display when the validator rejects a value as larger than `max` and
 * no `min` value is given.
 */

/**
 * @cfg {Number} min
 * The minimum allowed value.
 */

/**
 * @cfg {String} minMessage
 * The message to display when the validator rejects a value as less than `min` and
 * no `max` value is given.
 */

/**
 * @cfg {String} nanMessage
 * The message to display when the validator rejects a value as an invalid number.
 */
