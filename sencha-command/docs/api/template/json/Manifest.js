/**
 * @class Cmd.template.json.Manifest
 * This class describes a Sencha Cmd Template.
 *
 * **Templates are an experimental (unsupported) feature of Sencha Cmd.**
 * @private
 */

/**
 * @cfg {Object} conditional
 * An object keyed by relative path with values that contain boolean expressions. If the
 * expression is falsy the file at the relative path will not be generated.
 *
 *      "conditional": {
 *          "app/model/Base.js": "useBaseModel"
 *      }
 */

/**
 * @cfg {String} creator
 */

/**
 * @cfg {String} doc
 */

/**
 * @cfg {Cmd.template.json.Parameter[]} params
 * The set of inputs that can be passed to the template.
 */

/**
 * @cfg {String} title
 */

/**
 * @cfg {String} version
 */
