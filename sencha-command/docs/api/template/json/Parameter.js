/**
 * @class Cmd.template.json.Parameter
 * @private
 */

/**
 * @cfg {Mixed} defaultValue
 */

/**
 * @cfg {String} doc
 */

/**
 * @cfg {String} label
 */

/**
 * @cfg {String} name
 */

/**
 * @cfg {String} tip
 */

/**
 * @cfg {"boolean"/"integer"/"number"/"string"} [type="string"]
 */

/**
 * @cfg {"presence"/"range"/"exclusion"/"inclusion"} validators
 *
 *  - {@link Cmd.template.json.PresenceValidator `"presence"`}
 *  - {@link Cmd.template.json.ExclusionValidator `"exclusion"`} (if `type` is `"integer"`,
 *   `"number"` or `"string"`)
 *  - {@link Cmd.template.json.InclusionValidator `"inclusion"`} (if `type` is `"integer"`,
 *   `"number"` or `"string"`)
 *  - {@link Cmd.template.json.RangeValidator `"range"`} (if `type` is `"integer"` or
 *   `"number"`)
 */
