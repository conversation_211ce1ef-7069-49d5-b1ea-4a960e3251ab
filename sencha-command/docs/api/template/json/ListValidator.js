/**
 * @class Cmd.template.json.ListValidator
 * @extend Cmd.template.json.Validator
 * Validates that a value is or is not in a `list` of values. This validator is seldom used
 * directly. Instead one of its derived classes is used:
 *
 *  - {@link Cmd.template.json.ExclusionValidator `"exclusion"`}
 *  - {@link Cmd.template.json.InclusionValidator `"inclusion"`}
 *
 * Applies to parameters of type `"number"`, `"integer"` or `"string"`.
 * @private
 */

/**
 * @property {String} [type="list"]
 * @readonly
 */

/**
 * @cfg {Boolean} [exclusion=false]
 * Set to `true` if the validation should pass when a value is *not* in the `list`. By
 * default, the validation passes when a value is in the `list`.
 */

/**
 * @cfg {String[]} list
 * The values to match against.
 */

/**
 * @cfg {String} message
 * The message to display when the validator rejects a value.
 */
