/**
 * @class Cmd.codebase.json.Compressor
 */

/**
 * @cfg {"cmd"} compressor.type
 * In the current version only Sencha Cmd's own compressor (`"cmd"`) is supported. Support
 * for `"yui"` has been removed due to incompatibility with ES6 as well as its inability
 * to parse reserved words as object properties.
 */

/**
 * @cfg {"all"/"syntax"/"none"/"auto"} polyfills
 */

/**
 * @cfg {Boolean} renameFunctions
 * Enables / disables compressing function names
 */

/**
 * @cfg {Number} wrapLines
 * Sets the minimum number of characters to print before a line ending character is added.
 */

/**
 * @cfg {String} charset
 * Sets the output character set for the compression operation
 */

/**
 * @cfg {"ES5"/"ES6"} language
 */

/**
 * @cfg {Boolean} transpileOnly
 * Disables compression / optimization passes and only enables es 6 to 5 transpile passes
 */