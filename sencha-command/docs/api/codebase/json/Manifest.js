/**
 * @class Cmd.codebase.json.Manifest
 * This base class describes common configs for {@link Cmd.app.json.Manifest `app.json`}
 * and {@link Cmd.package.json.Manifest `package.json`} code bases.
 */

/**
 * @cfg {String/String[]} classpath
 * An array of paths or a comma-separated string of paths to folder or files to search for
 * classes. Any classes declared in these locations will be available to use in `Ext.define`
 * as "requires" or in calls to `Ext.require`.
 */

/**
 * @cfg {Cmd.codebase.json.Compressor} compressor
 * If not set to `null`, this config determines what JavaScript compressor is used to make
 * a "minified" build.
 */

/**
 * @cfg {Cmd.codebase.json.Asset[]} css
 * One or more CSS assets to include in the build.
 */

/**
 * @cfg {String} framework
 * The framework to use when building. This is typically "ext" but can be any key in the
 * {@link Cmd.workspace.json.Manifest#frameworks frameworks} collection of the workspace.
 */

/**
 * @cfg {String/String[]} overrides
 * An array of paths or a comma-separated string of paths to folder or files to search for
 * overrides. Any code declared in these locations will be automatically required and
 * included in the build. If any file defines an Ext JS override (using Ext.define with an
 * "override" property), that override will in fact only be included in the build if the
 * target class specified in the "override" property is also included.
 */

/**
 * @cfg {String[]} requires
 * A list of required packages.
 */

/**
 * @cfg {Cmd.codebase.json.Resource} resources
 */

/**
 * @cfg {Cmd.codebase.json.Sass} sass
 * This config controls how Cmd will process the application's Sass assets. Typically
 * these are located in the `./sass` folder.
 */

/**
 * @cfg {Object} slicer
 * Additional resources used during theme slicing operations.
 */

/**
 * @cfg {String} theme
 * The name of the theme package to use for generating CSS. Typical values include
 * `"theme-material"` (for the modern `toolkit`) and `"theme-triton"` (for both toolkits).
 */

/**
 * @cfg {"classic"/"modern"} toolkit
 * This config is needed when using the Ext JS 6+ `framework`.
 */

/**
 * @cfg {String} version
 * The version number.
 */
