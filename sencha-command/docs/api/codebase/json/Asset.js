/**
 * @class Cmd.codebase.json.Asset
 * These options apply to CSS and JS assets.
 */

/**
 * @cfg {Boolean} bundle
 * Set to true on one file to indicate that it should become the container for the
 * concatenated classes.
 */

/**
 * @cfg {Boolean} includeInBundle
 * Set to true to include this file in the concatenated classes.
 */

/**
 * @cfg {String} path (required)
 * Path to file. If the file is local this must be a relative path from the `app.json`
 * file.
 */

/**
 * @cfg {Boolean} [remote=false]
 * Specify as true if this file is remote and should not be copied into the build folder.
 * Defaults to false for a local file which will be copied.
 */

/**
 * @cfg {"delta"/"full"} update
 * If not specified, this file will only be loaded once, and cached inside localStorage
 * until this value is changed.
 *
 * You can specify:
 *   - `"delta"` to enable over-the-air delta update for this file.
 *   - `"full"` means full update will be made when this file changes.
 */

/**
 * @cfg {Boolean} [bootstrap=false]
 * Set to `true` to indicate that is a development mode only dependency. These files will
 * not be copied into the build directory or referenced in the generate app.json manifest
 * for the micro-loader.
 */
