/**
 * @class Cmd.codebase.json.Output
 * This object contains the configuration options that controls various build output. The
 * `base` config determines the base location of all the other output artifacts.
 */

/**
 * @cfg {String} base
 * The base location of all the other output artifacts. All relative `path` options are
 * relative to this location.
 */

/**
 * @cfg {String/Object} css
 * @cfg {Boolean} css.enable
 * @cfg {String} css.path
 */

/**
 * @cfg {String/Object} js
 * @cfg {Boolean} js.enable
 * @cfg {String} js.path
 * @cfg {"ES5"/"ES6"} js.version
 * @cfg {"all"/"minimum"/"used"} js.filter The js class filtering level when loading files for used packages.
 * "all" includes all files from the app, framework, and all required packages.  filtering is disabled.
 * "minimum" only includes files directly required by files from the application or used packages. 
 * "used" Includes all files from "minimum", plus all files for any package that is "required" by
 * both the app and a used package.
 * @cfg {"auto"/"usages"/"warn"} js.dependencies Controls how js class dependencies are processed.
 */

/**
 * @cfg {String} resources
 */

/**
 * @cfg {String/Object} slicer
 * @cfg {Boolean} slicer.enable
 * @cfg {String} slicer.path
 */
