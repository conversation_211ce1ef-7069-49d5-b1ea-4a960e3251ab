/**
 * @class Cmd.codebase.json.Sass
 * Options that control CSS build.
 *
 *      +-------+-------+---------+
 *      |       |       | base    |
 *      |       | theme +---------+
 *      |       |       | derived |
 *      |  etc  +-------+---------+
 *      |       | packages        |  (in package dependency order)
 *      |       +-----------------+
 *      |       | application     |
 *      +=======+=======+=========+
 *      |       |       | base    |
 *      |       | theme +---------+
 *      |       |       | derived |
 *      |  var  +-------+---------+
 *      |       | packages        |  (in package dependency order)
 *      |       +-----------------+
 *      |       | application     |
 *      +=======+=======+=========+
 *      |       |       | base    |
 *      |       | theme +---------+
 *      |       |       | derived |
 *      |  src  +-------+---------+
 *      |       | packages        |  (in package dependency order)
 *      |       +-----------------+
 *      |       | application     |
 *      +-------+-----------------+
 */

/**
 * @cfg {String} namespace
 * The root namespace to use when mapping `*.scss` files to classes in the
 * `sass/src` and `sass/var` directories.
 *
 * For example, `"App.view.Foo"` would map to `"sass/src/view/Foo.scss"`. If we changed
 * this to `"App.view"` then it would map to `"sass/src/Foo.scss"`.
 *
 * To style classes outside the app's root namespace, change this to `""`. Doing so will
 * change the mapping of `"App.view.Foo"` to `"sass/src/App/view/Foo.scss"`.
 */

/**
 * @cfg {String/String[]} [etc="sass/etc/all.scss"]
 * An array (or comma-separated string) of files containing extra Sass. These files are
 * automatically included in the Sass compilation. By default this is `"etc/all.scss"`.
 * This file can then contain `@import` statements to import desired utility code.
 *
 * All "etc" files are included at the top of the Sass compilation dependency order.
 *
 * Universal apps include a toolkit-specific file as well:
 *
 *      "etc": [
 *          "sass/etc/all.scss",
 *          "${toolkit.name}/sass/etc/all.scss"
 *      ]
 *
 * The replacement token `${toolkit.name}` will be replaced by `'classic'` or `'modern'`
 * depending on the toolkit used in the build.
 */

/**
 * @cfg {String/String[]} var
 * An array (or comma-separated string) of files or folders containing Sass variables or
 * other declarations. Files are automatically included in the Sass compilation. Folders
 * are used by the class name matching process to find files associated to particular
 * Ext JS classes.
 *
 * All "var" files are included in the middle of the Sass compilation dependency order.
 *
 * By default, this config is set to:
 *
 *      "var": [
 *          "sass/var/all.scss",
 *          "sass/var",
 *
 *          // Universal apps also have this entry:
 *          "${toolkit.name}/sass/var"
 *      ]
 */

/**
 * @cfg {String/String[]} src
 * An array (or comma-separated string) of files or folders containing Sass rules. Files
 * are automatically included in the Sass compilation. Folders are used by the class name
 * matching process to find files associated to particular Ext JS classes.
 *
 * All "src" files are included in the end of the Sass compilation dependency order.
 *
 * By default, this config is set to:
 *
 *      "var": [
 *          "sass/src",
 *
 *          // Universal apps also have this entry:
 *          "${toolkit.name}/sass/src"
 *      ]
 */

/**
 * @cfg {Object} generated
 * This objecct controls where Sencha tools save generated theme content.
 * @cfg {String} [generated.var="./sass/save.scss"] The file used to save variables edited
 * via Sencha Inspector and Sencha Themer. This file is processed as the last file so that
 * its `dynamic` vars override all others.
 * @cfg {String} [generated.src="./sass/save/"] This directory is used to save UI Mixin
 * calls generated by Sencha Themer.
 */
