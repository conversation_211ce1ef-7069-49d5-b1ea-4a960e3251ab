/**
 * @class Cmd.workspace.json.Manifest
 * @singleton
 * This is not an actual class or singleton but rather the contents of `'workspace.json'`
 * which Sencha Cmd uses to coordinate {@link Cmd.app.json.Manifest applications},
 * {@link Cmd.package.json.Manifest packages} and Sencha frameworks.
 *
 * A workspace is simply a root folder that contains the `workspace.json` file. It is used
 * as a coordination point for applications and the packages and frameworks they use.
 */

/**
 * @cfg {String[]} apps
 * An array of relative paths to the applications in this workspace. This is used to
 * perform bulk upgrade operations.
 */

/**
 * @cfg {Object} build
 * An object containing build settings.
 *
 * @cfg {String} build.dir
 * The default location to store build outputs.
 */

/**
 * @cfg {Object} frameworks
 * A named collection of {@link Cmd.workspace.json.Framework frameworks}. For example:
 *
 *      "frameworks": {
 *          "ext65": {
 *              "path": "ext",
 *              "version": "6.5.0"
 *          }
 *      }
 *
 * The name `"ext65"` can be used as the {@link Cmd.app.json.Manifest#framework framework}
 * value for applications in this workspace.
 */

/**
 * @cfg {Object} packages
 * This object controls where Sencha Cmd will download, extract and search for packages.
 *
 * @cfg {String/String[]} packages.dir
 * An array (or comma-separated string) of directories where Sencha Cmd will search for
 * locally developed packages. By default, this includes `./packages/local` and (for
 * backwards compatibility, `./packages`). These directories are only searched one level
 * deep to find packages.
 *
 * @cfg {String} packages.extract
 * The directory to which remote packages should be extracted. This defaults to the path
 * `./package/remote`. This folder should be excluded from version control (similar to
 * `node_modules`).
 */
