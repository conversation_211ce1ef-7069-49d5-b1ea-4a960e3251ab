/**
 * @class Cmd.workspace.json.Framework
 * Describes a Sencha framework (such as Ext JS).
 */

/**
 * @cfg {String} path
 * The relative path to the framework. In development mode, such as `sencha app watch`,
 * the framework must be part of the "web root" which defaults to the workspace root
 * folder (the directory containing `workspace.json`).
 */

/**
 * @cfg {String} version
 * The version of the framework.
 */
