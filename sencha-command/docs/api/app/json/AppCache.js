/**
 * @class Cmd.app.json.AppCache
 * This class describes the configs used to produce the HTML5 application cache manifest.
 *
 * **Important:** This technology has been deprecated in favor of Service Workers and, in
 * some browsers, Progressive Web Apps (PWA's). Unfortunately, Service Workers are
 * [not supported on all browsers](http://caniuse.com/#feat=serviceworkers).
 *
 * For more information about App Cache, check out these other resources:
 *
 *  - [AppCache on MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Using_the_application_cache)
 */

/**
 * @cfg {String[]} cache
 * The items to include in the CACHE MANIFEST section.
 */

/**
 * @cfg {String[]} network
 * The items to include in the NETWORK section
 */

/**
 * @cfg {String[]} fallback
 * The items to include in the FALLBACK section.
 */
