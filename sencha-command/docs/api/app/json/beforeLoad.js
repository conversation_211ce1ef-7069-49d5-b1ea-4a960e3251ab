/**
 * @member Ext
 * @method beforeLoad
 * This method is implemented by the application and is called by the `Ext.Microloader`
 * to determine the manifest to use. This method should set `Ext.manifest` to the manifest
 * object or the path to that resource. Typically, this is the name of a build profile
 * as defined in {@link Cmd.app.json.Manifest#output}.
 *
 * For example, if the application defines an `ios` profile and a `web` profile, this
 * method may look like the following:
 *
 *      var Ext = Ext || {};
 *      Ext.beforeLoad = function (tags) {
 *          Ext.manifest = tags.ios ? 'ios' : 'web';
 *      };
 *
 * @param {Object} tags The `Ext.platformTags` object.
 * @return {Function} An optional callback that if returned will be called when the
 * `Ext.manifest` has been loaded.
 * @return {Cmd.app.json.Manifest} return.manifest The loaded manifest object. This can be
 * manipulated as needed and will be used upon return.
 */
