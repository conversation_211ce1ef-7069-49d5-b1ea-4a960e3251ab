/**
 * @class Cmd.app.json.Output
 * @extend Cmd.codebase.json.Output
 * This object contains the configuration options that controls various build output. The
 * `base` config determines the base location of all the other output artifacts.
 */

/**
 * @cfg {String/Object} appCache
 * @cfg {Boolean} appCache.enable
 * Setting this property to `true` will cause the Application Cache manifest file to be
 * generated based on the {@link Cmd.app.json.Manifest#appCache appCache} object. This file
 * will then be injected into the `index.html` file of the built application.
 */

/**
 * @cfg {String/Object} manifest
 * @cfg {Boolean} manifest.embed
 * @cfg {Boolean} manifest.enable
 * @cfg {String} manifest.path
 */

/**
 * @cfg {String/Object} microloader
 * @cfg {Boolean} microloader.embed
 * @cfg {Boolean} microloader.enable
 * @cfg {String} microloader.path
 */

/**
 * @cfg {String/Object} page
 * @cfg {Boolean} page.enable
 * @cfg {String} page.path
 */
