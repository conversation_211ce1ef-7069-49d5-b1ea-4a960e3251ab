/**
 * @class Cmd.app.json.Manifest
 * @extend Cmd.codebase.json.Manifest
 * This class is a description of the contents of `'app.json'` file which Sencha Cmd
 * compiles to produce `Ext.manifest`.
 */

/**
 * @cfg {Cmd.app.json.AppCache} appCache
 * Settings used to generate a `cache.manifest` file.
 */

/**
 * @cfg {String} archivePath
 * Directory path to store all previous production builds. Note that the content
 * generated inside this directory must be kept intact for proper generation of
 * deltas between updates.
 */

/**
 * @cfg {String/Object} bootstrap
 * Options for development-mode artifacts. If this value is a string, it is equivalent to
 * setting the `bootstrap.base` value.
 *
 * @cfg {String} [bootstrap.base="${app.dir}"] The base location where development-mode
 * artifacts are stored. By default they are stored in the same location as `app.json`
 * itself. Relative paths for other `bootstrap` artifacts are based at this location.
 *
 * @cfg {String} [bootstrap.css="bootstrap.css"]
 * The development mode CSS file.
 *
 * @cfg {String} [bootstrap.microloader="bootstrap.js"]
 * The path to the main JavaScript file to load in development mode that will then load
 * the files described in the `bootstrap.manifest`. This file will be generated with the
 * `Ext.Boot` and `Ext.Microloader` classes.
 *
 * @cfg {String/Object} [bootstrap.manifest]
 * When this config is a string, it is equivalent to setting `bootstrap.manifest.path`.
 * Otherwise this config is an object with optional `path` and `exclude` properties.
 *
 * @cfg {String} [bootstrap.manifest.path="bootstrap.json"]
 * The location of the development-mode manifest file. This file describes the files to
 * load for the application and framework.
 *
 * @cfg {String} [bootstrap.manifest.exclude]
 * A comma-separated list of top-level keys to remove from the generated manifest object.
 * One key that may be desirable to remove is `loadOrder`, which is used to load files in
 * the same order as a production build, but can be rather large.
 */

/**
 * @cfg {Object} builds
 * This object's properties are "build profiles". You can add as many as you need to
 * produce optimized builds for devices, themes, locales or other criteria. Your
 * `Ext.beforeLoad` function is responsible for selecting the desired build profile by
 * setting `Ext.manifest` to one of these names.
 */

/**
 * @cfg {Object} cache
 *
 * @cfg {Boolean} cache.enable This property controls whether localStorage caching of the
 * manifest file. If disabled no deltas will be generated during a build and full updates
 * will be disabled.
 *
 * @cfg {Boolean/String} [cache.deltas] Determines whether the localStorage cache of assets
 * will use "delta patching" to update assets.
 *
 * If set to a String, that value will be used as the path to where deltas will be generated
 * relative to the build.
 *
 * If set to `true` the default path of `"deltas"` will be used. If set to `false` or if
 * this property is not present, deltas will not be generated.
 */

/**
 * @cfg {Object} cordova
 * TODO
 * @cfg {Object} cordova.config
 * Cordova configuration object.
 */

/**
 * @cfg {Cmd.app.json.Fashion} fashion
 * The options that control the CSS build using the Sencha Fashion theme compiler.
 */

/**
 * @cfg {String} framework
 * The framework of this application. This is typically "ext" but can be any key in the
 * {@link Cmd.workspace.json.Manifest#frameworks frameworks} collection of the workspace.
 */

/**
 * @cfg {String} id
 * Uniquely generated id for this application, used as prefix for localStorage keys.
 * Normally you should never change this value.
 */

/**
 * @cfg {String[]} ignore
 * File / directory name pattern to ignore when copying to the builds. Must be a
 * valid regular expression.
 */

/**
 * @cfg {String} indexHtmlPath
 * The relative path to the application's markup file (html, jsp, asp, etc.).
 * Typically "index.html".
 */

/**
 * @cfg {Cmd.codebase.json.JS[]} js
 * One or more JavaScript assets to include in the build (or load remotely). Typically
 * this config is set to something like:
 *
 *      "js": [{
 *          "path": "app.js",
 *          "bundle": true
 *      }]
 */

/**
 * @cfg {Object} loader
 * Settings for the Ext JS dynamic loader (`Ext.Loader`).
 * @cfg {Boolean/String} [loader.cache=false] When set to `false` the loader uses the
 * `loader.cacheParam` to disable cached responses. When set to `true`, the loader does
 * not disable browser caching. In a `production` build this is usually set to the build
 * timestamp using the `${build.timestamp}` variable.
 * @cfg {String} [loader.cacheParam="_dc"] The URL query parameter to use to disable the
 * browser cache. For example `"?_dc=12345"`.
 */

/**
 * @cfg {String} name
 * The application's name (typically also its root namespace).
 */

/**
 * @cfg {String} namespace
 * The application's root namespace. This property can be provided if the `name` property
 * is used in ways that do not match with the JavaScript namespace.
 */

/**
 * @cfg {Cmd.app.json.Output} output
 * The options used to control build output.
 */

/**
 * @cfg {Object} phonegap
 * This config is the same as the `cordova` config but applies to the PhoneGap packager.
 * @cfg {Object} phonegap.config
 * PhoneGap configuration options.
 */

/**
 * @cfg {String[]} uses
 * A list of packages that will be used by the application. These packages are built so
 * that they can be dynamically loaded using the `package-loader` package.
 */

/**
 * @cfg {Cmd.app.json.progressive.Progressive} progressive
 * This is the configuration object for Progressive Web Applications
 */

/**
 * @cfg {Cmd.app.json.packager.Cordova} cordova
 * The config options for the cordova packager process
 */

/**
 * @cfg {Cmd.app.json.packager.Phonegap} phonegap
 * The cofig options for the phonegap packager process
 */

/**
 * @cfg {"cordova"/"phonegap"} packager
 * When configured on a build profile, selects the desired native packager to use for that
 * build profile.
 */