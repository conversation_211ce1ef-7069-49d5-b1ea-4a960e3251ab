/**
 * @class Cmd.app.json.packager.Phonegap
 */

/**
 * @cfg {String} name
 * This is the name of your phonegap application. This will default to your Sencha App name.
 * This is only used once during Phonegap app creation and cannot be changed after.
 */

/**
 * @cfg {String} id
 * This will be your package name for Android and your Bundle Identifier for iOS
 * This is only used once during Phonegap app creation and cannot be changed after
 */

/**
 * @cfg {String} platform
 * a single platform to build, run or emulate
 * platform supported locally: android, ios, wp8, Blackberry 10
 * platform supported remotely: android, ios, wp8
 */

/**
 * @cfg {Boolean} remote
 * This boolean will determine if the build should be run on Phonegap's remove server 'http://build.phonegap.com'
 * setting remote to true will attempt to build on the cloud.
 * To properly use this one must set the following properties in there local.properties file (if this file does not exist create it in your app root)
 * phonegap.remote.username=<EMAIL>
 * phonegap.remote.password=mys3cr3tp@ssw0rd
 */

/**
 * @cfg {<PERSON>ole<PERSON>} verbose
 * This boolean will determine if all phonegap commands will have verbose output or not.
 * to properly see this run sencha command with the '-info' flag like the following
 */

/**
 * @cfg {String} path
 *  The path this builds phonegap project should be created in.
 *  This is only used once during Phonegap app creation if changed this will result in a new phonegap application being generated
 *  This defaults to your {app.dir}/phonegap
 */



