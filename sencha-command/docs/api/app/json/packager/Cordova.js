/**
 * @class Cmd.app.json.packager.Cordova
 */

/**
 * @cfg {String} name
 *  This is the name of your cordova application. This will default to your Sencha App name.
 *  This is only used once during Cordova app creation and cannot be changed after.
 */

/**
 * @cfg {String} id
 * This will be your package name for Android and your Bundle Identifier for iOS
 * This is only used once during Cordova app creation and cannot be changed after
 */

/**
 * @cfg {String} platform
 *  can be a platform or a space seperated list of platform (ios android)
 *  platform supported on mac: ios, amazon-fireos, android, blackberry10, firefoxos
 *  platform supported on win: wp7, wp8, windows8, amazon-fireos, android, blackberry10, firefoxos
 */

/**
 * @cfg {Boolean} verbose
 * This boolean will determine if all cordova commands will have verbose output or not.
 * to properly see this run sencha command with the '-info' flag like the following
 * sencha -info app run [buildname]
 */

/**
 * @cfg {String} path
 * The path this builds cordova project should be created in. This defaults to your {app.dir}/cordova
 */


/**
 * @cfg {String} target
 * This is the target for emulator/simulator commands.
 * On Android is the name of your Android Virtual Device
 * For iOS it is one of the following:
 * "iPhone (Retina 3.5-inch)"
 * "iPhone (Retina 4-inch)"
 * "iPhone"
 * "iPad"
 * "iPad (Retina)"
 */