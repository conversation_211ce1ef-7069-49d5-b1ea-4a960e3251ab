/**
 * @class Cmd.app.json.progressive.Progressive
 * This is the object that configures the Progressive Web Application portion of the 
 * Sencha Cmd build process.
 * 
 * For more information, see https://developers.google.com/web/progressive-web-apps/
 */

/**
 * @cfg {Cmd.app.json.progressive.Manifest} manifest
 * Configures the manifest for the PWA
 */

/**
 * @cfg {Cmd.app.json.progressive.ServiceWorker} serviceWorker
 * Configuration data to set up the generated service-worker.js file.
 */
