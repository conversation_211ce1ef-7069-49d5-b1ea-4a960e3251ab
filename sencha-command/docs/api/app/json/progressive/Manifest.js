/**
 * @class Cmd.app.json.progressive.Manifest
 */

/**
 * @cfg {String} name
 */

/**
 * @cfg {String} short_name
 */

/**
 * @cfg {Object[]} icons
 * @cfg {String} icons.src The path to the icon file.
 * @cfg {String} icons.sizes The sizes provided by the icon ("256x256")
 */

/**
 * @cfg {String} theme_color
 */

/**
 * @cfg {String} background_color
 */

/**
 * @cfg {String} display
 */

/**
 * @cfg {"landscape"/"protrait"} orientation
 */

/**
 * @cfg {String} start_url
 * The initial url to load for the PWA. Ex: "/index.html"
 */



