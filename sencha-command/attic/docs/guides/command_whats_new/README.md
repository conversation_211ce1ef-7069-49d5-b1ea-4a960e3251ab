# What's New & Installation

This guide describes what changed since the last [Sencha Cmd](http://www.sencha.com/products/sencha-cmd/)
release, and explains how to install Sencha Cmd.

For more information, see [Introduction to Sencha Cmd](#!/guide/command).

## What's New 

Sencha Cmd provides these new features - All work on Sencha Ext JS and Sencha Touch:

<table style="width: 90%" border="1">
<tr>
<th style="width: 20%">Command</th>
<th style="width: 60%">Description</th></tr>
<tr><td><b>sencha app refresh</b></td>
<td>Updates metadata such as loader paths or class aliases.</td></tr>
<tr><td><b>sencha app resolve</b></td>
<td>Creates a dynamic dependency resolution step (called <tt>resolve</tt>
that uses a headless
WebKit (PhantomJS) to load your application and extract the 
dynamic loader's history. This determines dependencies and can help if your
application does not fully declare its dependencies using requires and/or 
uses statements.<br>
<b>Note</b> Before using this command, enable access by setting 
the following property in the <tt>.sencha/app/build.properties</tt> file:
<pre>skip.resolve=0</pre>
<b>Caution</b>: If your application dynamically loads classes by 
browser or platform, this command may produce incorrect results.</td></tr>
<tr><td><b>sencha app watch</b></td>
<td>Continually updates bootstrap.js and app.js files for a project 
after you change a component in your application. Also rebuilds Sass 
after CSS changes are made. Runs in the background until you type CTRL+C. 
This command is scoped by the application project in which you run the command.<br>
<b>Note</b>: This command requires Java 7. All other features of Cmd continue to work with Java 6.</td></tr>
<tr><td><b>sencha cordova *</b><br><b>sencha phonegap *</b></td>
<td>Enables access to 
<a href="http://cordova.apache.org/docs/en/3.0.0/">Apache Cordova</a> 
APIs and packaging, as well as 
<a href="http://phonegap.com/">PhoneGap</a>. For more information, see 
<a href="http://docs.sencha.com/touch/#!/guide/cordova">Cordova and PhoneGap App</a>.</td></tr>
</table>

**Note** Ext JS currently does not provide support for Cordova and PhoneGap mobile devices.

## Installing Sencha Cmd

To install Sencha Cmd:
<ol>
	<li>Download 
	<a href="http://www.google.com/chrome">Chrome</a> or 
    <a href="http://www.apple.com/safari">Safari</a>.
	Windows development computers can use 
	<a href="http://windows.microsoft.com/en-us/internet-explorer/ie-10-worldwide-languages">Internet Explorer 10</a> or later.</li>
	<li>Download <a href="https://adoptium.net/en-GB/temurin/releases">Java Runtime Environment</a> 
	version 1.7 (1.6 also works, but 1.7 is required for the Cmd 4.0 app watch feature). 
	Sencha Cmd is written in Java and needs the JRE to run.</li>
	<li>Download <a href="http://www.ruby-lang.org/en/downloads/">Ruby version 1.9.3</a> 
		(Ruby version 2.0 doesn't work with Sencha Cmd).
		<ol>
		<li>Windows: Download Ruby 1.9.3.n from 
		<a href="http://rubyinstaller.org/downloads/">rubyinstaller.org</a>. 
		Download the RubyInstaller .exe file and run it.</li>
		<li>Mac: Ruby is pre-installed. You can test which version 
		you have with the <b>ruby -v</b> command. If you have version 2.0, 
		download the <a href="https://rvm.io/">Ruby version manager</a> (rvm) 
		and use this command to
		download and install Ruby:
		<br><b>rvm install 1.9.3 --with-gcc=clang</b><br>
		Set your PATH variable to point to the Ruby 1.9.3 install directory.</li>
 		<li>Ubuntu: Use <b>sudo apt-get install ruby1.9.3</b> to download 
		and install Ruby 1.9.3.</li>
		</ol>
	</li>
	<li>To view Sencha Cmd options after installing Sencha Cmd, 
		type <b>sencha</b> from the command line:<br>
		Windows: Press the Windows&nbsp;key&nbsp;+&nbsp;<b>r</b> and 
		type <b>cmd</b> to get a Command Prompt.<br>
		Mac: Open a Terminal from Finder, Go, Utilities, Terminal.<br>
		Ubuntu: Click the Ubuntu Dashboard and type <b>terminal</b> 
		in the search box.</li>
	<li>Create a project directory where you want to serve applications 
		and ensure the directory is writable:<br>
		Windows: Right-click the directory name, click <b>Properties</b>, 
		and uncheck <b>Read-only</b> on the General tab.<br>
		Mac and Ubuntu: Use <b>chmod o+w</b> <i>&lt;dir_name&gt;</i></li>	
	<li>Start the Sencha Cmd web server to serve the applications directory: 
		<pre>sencha web start</pre> 
		This command uses the default port 1841 and the current directory. 
		The web server runs exclusively in the command line window 
		until you stop the server by typing CTRL+C. You can also stop the web
		server from another command line window with:
		<br><b>sencha web stop</b>
		<br>You can use the <tt>-port</tt> <i>port</i> option to specify another port, and
		you can specify another directory with the <tt>-map</tt> <i>directory_name</i>
		option.
		<br>Access the Sencha Cmd web server using: 
		<tt>http://localhost:1841/&lt;dir_name&gt;/&lt;app_name&gt;</tt>
		</li>
</ol>	

<b>Note</b> If you need support for server-side scripting or PHP, 
you can get another web server such as 
<a href="http://www.apachefriends.org/en/xampp.html">XAMPP</a>. 
If you use XAMPP, create your project directory in the XAMPP <i>htdocs</i> directory.


## Sencha Cmd Information

<ul>
<li>[Introduction to Cmd](#!/guide/command)</li>
<li>[Sencha Documentation](http://docs.sencha.com/)</li>
<li>[Sencha Training](http://www.sencha.com/training/)</li>
<li>[Sencha Support](http://www.sencha.com/support/)</li>
<li>[Sencha.com](http://sencha.com/)</li>
</ul>
