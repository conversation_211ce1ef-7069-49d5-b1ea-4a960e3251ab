# Sencha Cmd Packages

[Sencha Cmd](http://www.sencha.com/products/sencha-cmd/)
includes the Sencha Package Manager. There are two basic problems that
packages are designed to solve: consumption and distribution. This guide focused on these
topics. See also [Creating Sencha Cmd Packages](#!/guide/command_package_authoring) for
information about creating and sharing a package.

## The `"packages"` Folder

All workspaces generated by Sencha Cmd have a `"packages"` folder at the root. The location
of this folder can be configured, but regardless of its location, the role of the
`"packages"` folder is to serve as the storage of all packages used by the applications
(or other packages) in the workspace.

Packages will be added to the `"packages"` folder when they are required by an application
in the workspace or when you call `sencha generate package`.

## Requiring Packages in Applications

Regardless of the origin of a package (whether it was created locally or downloaded from
a remote repository - see below), consuming the package is the same: you add an entry to
the `requires` array in `"app.json"`. For demonstration purposes, we have added a simple
package that you can experiment with:

    {
        "name": "MyApp",
        "requires": [
            "ext-easy-button"
        ]
    }

Given the above requirements, `sencha app build` and `sencha app refresh` will both now
perform the steps needed to integrate the package in to your application. Typically,
after changing package requirements, you will need to run `sencha app refresh` so that
the metadata required to support "dev mode" is up to date.

Whichever command you run, Sencha Cmd will download and expand the package to your
`"packages"` folder. After this you will find a `"packages/ext-easy-button"` folder in
your workspace.

## Local Packages

One use of packages is simply to hold code or themes that are available
for multiple applications in a workspace. These packages need never be distributed (beyond
source control) to provide value to your development.

In previous releases of Sencha Cmd, you could only share code in a workspace by using the
`workspace.classpath` property in your `.sencha/workspace/sencha.cfg"` file. While this
still works, this mechanism was limited because you could not easily share Sass/CSS
styling or resources such as images. Using packages, you can do all of these things.

To add a package to your workspace, you just generate the package:

    sencha generate package -type code common

This package is marked as `local: true` in its `"package.json"`. This flag prevents Sencha
Cmd from ever overwriting the package with a version from a remote repository (see below).

Then add "common" as a requirement to your application's `"app.json"` as described above:

    {
        "name": "MyApp",
        "requires": [
            "common"
        ]
    }

For more details, especially regarding how to distribute packages to others, see
[Creating Sencha Cmd Packages](#!/guide/command_package_authoring).

## Remote Packages

While local packages can be very valuable in large applications, one of the most useful
aspects of packages is the ability to distribute packages for others to consume. In fact
we have already used a remote package: `"ext-easy-button"`.

Packages are shared and distributed using package repositories. Sencha Cmd automatically
creates a "Local Repository" for caching packages as well as for publishing packages. The
role of the local repository for package authoring is not discussed in this guide. For
details on that topic see [Creating Sencha Cmd Packages](#!/guide/command_package_authoring).

### Local Repository

Many operations implicitly use the local repository such as the `"ext-easy-button"` example
above. In that example, Sencha Cmd followed these basic steps when it encountered the
`requires` statement in `"app.json"`:

  * Look in the workspace to see if the package is already present.
  * Check the local repository to see if there is a version already downloaded.
  * Look at the set of remote repositories defined to see if any has the package.
  * Download the package from the remote repository and add to the local repository.

Once the package has been downloaded, subsequent requirements for this package will not
require downloading - the package will be found in the local repository.

#### Location of the Local Repository

The local repository is created in a folder "beside" the various versions to facilitate
caching. For example, the default install directory of Sencha Cmd on Windows for user Foo
might be something like this:

    C:\Users\<USER>\bin\Sencha\Cmd\n.n.n.n

Given that install directory, the local repository would be located at:

    C:\Users\<USER>\bin\Sencha\Cmd\repo

This can be changed by editing the `"sencha.cfg"` in the Sencha Cmd install folder.

The contents of the local repository are discussed further in
[Creating Sencha Cmd Packages](#!/guide/command_package_authoring).

### Remote Repositories

In order to download packages to the local repository, Sencha Cmd must know about remote
repositories. By default, Sencha Cmd automatically adds the Sencha Package Repository as
a remote repository named "sencha".

To see the list of remote repositories, run the `sencha repository list` command:

    > sencha repository list
    Sencha Cmd vn.n.n.xxx
    [INF] Remote repository connections (1):
    [INF]
    [INF]     sencha - http://cdn.sencha.com/cmd/packages/

You can add and remove repositories from this list using `sencha repository add` and
`sencha repository remove` commands. Your local repository is in fact a valid remote
repository for others if you chose to host it. For details on this, see
[Creating Sencha Cmd Packages](#!/guide/command_package_authoring).

## The Package Catalog

The set of packages available to use is listed in the "catalog". You can display the
contents of the global catalog using this command:

    sencha package list

When you list packages you will notice that the listing includes names and versions.

### Name Assignment

Because Sencha Cmd's repository management is distributed and you can add or remove remote
repositories as you see fit, there is no Universal namespace of packages. If you retain
the default "sencha" connection to the Sencha Package Repository, then you can view the
content of that repository as a naming standard.

Packages published by Sencha will use names of the following forms:

  * sencha-*
  * ext-*
  * touch-*
  * cmd-*

All package names beginning with the above prefixes are reserved by Sencha with respect
to the Sencha Package Repository. It is recommended that you avoid conflicting with these
names even if you disconnect from the Sencha Package Repository.

## Version Management

To connect packages and their consumers (that is, applications or other packages), it is
important to consider the versions involved. To help manage this, all packages have version
numbers which are used by the `requires` statement to handle version restrictions.

### Package Versioning

All packages are described by the combination of their name and their version. For each
version of a package, however, there is another version number that describes its level
of backwards compatibility. This version is a statement made by the package creator that
they believe the particular version of their package to be backwards compatible to some
specific previous version level.

In the `"package.json"` descriptor there are two important properties: `version` and
`compatVersion`. For example:

    {
        ...
        "version": "n.n.n",
        "compatVersion": "2.4.2",
        ...
    }

This number must be of this format:

    \d+(\.\d+)*

### Version Restrictions

When using the `requires` property above we only specified the package name to keep the
example as simple as possible. What that means precisely is "the latest version". In some
cases this is acceptable, but it can be a risky requirement should that package's next
release be a complete redesign and offer no level of backwards compatibility.

To protect your application from this, we can change the `require` statement to be very
restrictive and lock down the version number we want:

    {
        "name": "MyApp",
        "requires": [
            "ext-easy-button@1.0"
        ]
    }

This approach has its place, but it prevents even maintenance releases of the package
from being picked up. In final stages of a project, this may be exactly what is desired,
but during development perhaps we want to be a little less restrictive and accept any
compatible version.

The following change will do that:

    {
        "name": "MyApp",
        "requires": [
            "ext-easy-button@1.0?"
        ]
    }

The above requests the latest available version of `"ext-easy-button"` that has described
itself as backwards compatible with version 1.0.

Other ways to restrict version matching are:

  * -1.2 (no lower limit - up to version 1.2)
  * 1.0- (no upper limit - version 1.0 or higher)
  * 1.0+ (same as above - version 1.0 or higher)
  * 1.0-1.2 (versions 1.0 up to 1.2 inclusive)
  * 1.0-1.2? (compatible with versions 1.0 up to 1.2 inclusive)

### Resolving Versions

Given all of the desired and available versions, Sencha Cmd will determine the best set
of matching packages and ensure that these are in your workspace.

If there are mutually exclusive requirements this process may fail. In this case, you will
see an error message explaining what requirements could not be satisfied.
