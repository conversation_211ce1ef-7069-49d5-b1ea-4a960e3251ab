[{"title": "Getting Started", "items": [{"name": "command", "url": "guides/command", "title": "Introduction to <PERSON><PERSON> Cmd", "description": "Understanding how to use Sencha Cmd"}, {"name": "command_whats_new", "url": "guides/command_whats_new", "title": "What's New and Install Information", "description": "What's New and Install Information"}, {"name": "command_code", "url": "guides/command_code", "title": "Coding Guidelines", "description": "Getting the most from the Sencha Cmd compiler"}]}, {"title": "<PERSON><PERSON>", "items": [{"name": "command_app_touch", "url": "touch/command_app", "title": "Generating Sencha Touch Apps", "description": "Generating applications, and automating deployment and packaging"}, {"name": "native_packaging", "url": "touch/native_packaging", "title": "Packaging Touch Apps for Mobile Devices", "description": "Packaging Sencha Touch apps to run on mobile devices"}]}, {"title": "Sencha Ext JS", "items": [{"name": "command_app_ext42", "url": "ext/command_app", "title": "Generating Ext JS Apps", "description": "Generating Ext JS Applications"}, {"name": "command_app_ext41", "url": "ext41/command_app", "title": "Generating Legacy Ext JS Apps", "description": "Generating Legacy Ext JS Applications"}]}, {"title": "Advanced Ext JS", "items": [{"name": "command_app_single", "url": "ext/command_app_single", "title": "Single-Page Ext JS Apps", "description": "Creating single page applications using the compiler and package manager"}, {"name": "command_app_multi", "url": "ext/command_app_multi", "title": "Multi-Page Ext JS Apps", "description": "Creating multi-page applications using the compiler and package manager"}, {"name": "command_slice", "url": "ext/command_slice", "title": "Image Slicing for IE", "description": "Generating images for Internet Explorer to display gradients and rounded corners"}]}, {"title": "Packages", "items": [{"name": "command_packages", "url": "guides/command_packages", "title": "Using Packages", "description": "Using Packages in an application"}, {"name": "command_package_authoring", "url": "guides/command_package_authoring", "title": "Authoring and Distributing Packages", "description": "Authoring and distributing packages"}]}, {"title": "Advanced Topics", "items": [{"name": "command_workspace", "url": "guides/command_workspace", "title": "Managing Workspaces", "description": "Managing multiple applications and packages"}, {"name": "command_advanced", "url": "guides/command_advanced", "title": "Advanced Sencha Cmd", "description": "Using Sencha Cmd - Advanced"}, {"name": "command_ant", "url": "guides/command_ant", "title": "Ant Integration", "description": "Utilizing Sencha Cmd Ant tasks"}, {"name": "command_compiler", "url": "guides/command_compiler", "title": "Compiler Reference", "description": "Working with the Sencha Cmd Compiler"}, {"name": "command_compiler_meta", "url": "guides/command_compiler_meta", "title": "Code Metadata", "description": "Generating and using code metadata"}, {"name": "command_reference", "url": "guides/command_reference", "title": "Command Line Reference", "description": "Command Line Reference"}]}]