# Sencha Cmd Package Activation

[Sencha Cmd](http://www.sencha.com/products/sencha-cmd/)
provides a limited form of Digital Rights Management (DRM) for package
authors that want to restrict access to certain features of their packages. This is not
intended for all package authors but only for those that want to sell their packages and
require that users acquire an activation key in order to unlock the full content of the
package.

For general information for package authors see 
[Creating Sencha Cmd Packages](#!/guide/command_package_authoring).
For information on using packages refer to [Sencha Cmd Packages](#!/guide/command_packages).

## Protecting Package Content

Packages are essentially ZIP files with some specific additional content recognized by
Sencha Cmd. This consists primarily of `"package.json"` (the package descriptor). Protected
content is included in this ZIP file but is encrypted using a key you specify. The content
can only be decrypted by package users if they have a proper Activation Key.

## Content Lockers

To enable content protection for a package, you add an object to `"package.json"` called
`locker`. A simple example of this might be to protect the `"src"` folder:

    {
        "name": "some-package",
        "type": "code",
        "creator": "SomeCompany",
        "version": "4.0",
        "compatVersion": "1.0",
        "format": "1",
        "local": true,

        "locker": {
            "src": {
                "key": "abc123",
                "content": [{
                    "dir": "src"
                }],
                "trust": [
                    "Sencha"
                ]
            }
        }
    }

The `locker` object consists of one or more named lockers. Each locker has a `key` that
unlocks some set of files, that is, its `content`. Typically there is only one locker in
the `locker` collection but if there are multiple, the name of each locker is its key in
the collection. In the above example, "src" is the name of the one locker instance. The
locker name must be unique in a given package.

There are 3 essential properties for a locker:

  * **`key`** - The encryption key used to protect the locker. This key is used for the
  encryption algorithm and should be a suitably secure token. This is removed during
  package signing.
  * **`content`** - An array of directory/file descriptions to include in the locker. Any
  files included in a locker are automatically removed from the unprotected package ZIP.
  * **`trust`** - An array of named parties that are allowed to generate Activation Keys
  for the package. By default, if `trust` is not specified, only the package creator is
  authorized to generate these keys. If you plan to submit the package to Sencha Market,
  you should list "Sencha" as shown to allow package purchases to generate Activation
  Keys.

### Package Signing

When Sencha Cmd first introduced packages, "signing" was entirely behind the scenes and
was basically just a hash used to detect file corruption. To protect package content, the
public and private keys used for package integrity are now also used to securely transfer the
`key` for each `locker` instance.

Another way to say this is that the `key` to a `locker` is encrypted so that only those
in the `trust` array can decrypt it. Each of these parties has their own public/private
key pair so the locker `key` is encrypted once for each trusted party.

#### Signed Package Descriptor

For reference, below is what the `"package.json"` file looks like after signing. This is
provided for explanatory purposes and is not something you would add yourself.

    {
        "name": "some-package",
        "type": "code",
        "creator": "SomeCompany",
        "version": "4.0",
        "compatVersion": "1.0",
        "format": "1",
        "local": true,

        "signatures": [{
            "name": "SomeCompany",
            "uuid": "8ef16ea3-1a83-4a03-94cc-c495c306fc7c",
            "created": "2013-04-09T06:08:10Z",
            "algorithm": "SHA1withRSA",
            "nonce": "iP83HOpi4q4\u003d",
            "signature": "...base64 data..."
        }],

        "seal": {
            "src": {
                "data": "...",
                "keys": {
                    "8ef16ea3-1a83-4a03-94cc-c495c306fc7c": "...base64 data..."
                }
            }
        }
    }

The `signatures` array is as before - an integrity check on the ZIP file.

The `locker` has been replaced by the `seal`. The `data` property contains the locker
options (see below) and is encrypted with the locker's `key` to avoid tampering. The `key`
to the locker is encrypted using the public key of the package creator and each member in
the `trust` array for the locker. This means that only the package author and those they
trust can retrieve the locker's `key` because doing so requires the corresponding private
key(s).

### Content Options

In addition to simply protecting whole folders as in the initial example, there are other
options that may be useful.

#### File Selection

By default, a `content` entry needs only a `dir` property:

    "locker": {
        "src": {
            "key": "abc123",
            "content": [{
                "dir": "src"
            }],
            "trust": [
                "Sencha"
            ]
        }
    }

This directory is relative to the package root folder. If a path is required, always use
the "/" as the separator.

The above form has the default meaning of "include all files and sub-folders". This can
be refined by adding a `files` array.

To include all files (deeply) in the `"src"` folder except markdown (`".md"`) files:

    "content": [{
        "dir": "src",
        "files": [
            "**/*",
            "!**/*.md"
        ]
    }]

The syntax is an [Ant Pattern](http://ant.apache.org/manual/dirtasks.html#patterns) as these
are internally converted to an [Ant FileSet](http://ant.apache.org/manual/Types/fileset.html).
The leading "!" on the second entry marks that entry as an exclude. For those familiar
with Ant, the above is equivalent to this:

    <fileset dir="src">
        <include name="**/*"/>
        <exclude name="**/*.md"/>
    </fileset>

#### Overlays

Another common use case is to overwrite certain files with "enhanced" versions once the
package is activated. For example, the default license file could be a trial license or
GPL. This can be replaced by the content of a `locker` on activation.

For example, to replace the license file in the base package:

    "content": [{
        "dir": "src"
    }, {
        "dir": "locker/src/license",
        "rebase": "license"
    }]

In this example, we have created a new folder in the package structure to hold content
that is specific to the "src" locker license. The extraction path, however, is different
and so we use the `rebase` option to change the base folder for extraction. In the above,
the content of `"locker/src/license"` overwrites whatever is in the normal package's
`"license"` folder (if the appropriate Activation Key is present).

### Locker Options

While the core `key`, `content` and `trust` properties are all that is typically needed,
there are additional options on a `locker`.

#### Version Ranges

An Activation Key is generated for a specific package version. By default, the Activation
Key is only valid for that version. To allow an Activation Key to activate other versions
you can use the `downVersion` and/or `upVersion` properties:

    "version": "4.0",
    "locker": {
        "src": {
            "key": "abc123",
            "downVersion": "2.0",
            "upVersion": "2.5",
            "content": [{
                "dir": "src"
            }],
            "trust": [
                "Sencha"
            ]
        }
    }

The `downVersion` above allows the Activation Key generated for version 4.0 to be used
back all the way "down" to version 2.0. This is important if you want to allow users to
"downgrade" to an older package version.

The `upVersion` indicates that version 3.0 of the package will allow Activation Keys from
version 2.5 or higher to be "upgraded" to this version.

It is helpful to remember that Activation Keys are generated at fixed moments in time for
the package user. As the package creator, it is important to allow your users some version
upgrade/downgrade flexibility with that key. When setting `downVersion`, think of users
that activate at the current version. When setting `upVersion` it is important to think
about users that activated a previous version.

This information is stored in the `seal` to avoid tampering with these values.

## Activation Keys

To control user access this protected content, Sencha Cmd uses Activation Keys. These
are objects that can only be created by package creator or a party that was listed in the
`trust` array of the package locker.

An Activation Key is like a `seal` in two important ways: it contains the locker's `key`
and it is encrypted using a public key so that only the intended party can decrypt it. In
this case, however, the Activation Key is encrypted with the public key of the user that
is being granted access to the protected content.

The Activation Key also contains a description of what the user has been granted (that is,
the "entitlement"). At present, this only consists of the `downVersion` for the locker so
that the key can be used for older versions of the package.

Activation Keys are generated and stored in the repository alongside the packages that
they activate.

    pkgs/
        ...
        some-package/
            1.0.0/
            ...
            keys/
                575ef301-aa66-4a03-23cd-7695cf06ec72.json

These keys are named by the `id` generated for the user. Because these keys can only be
used/decrypted by that user, there is no issue storing them in the public repository.

### Content of Activation Keys

Only one file is stored for a particular user so this file holds all of the Activation
Keys that they have been granted. For example, the above JSON file might look like this:

    {
        "activate": [{
            "id": "c689b7a9-561e-45a8-ae62-dd1a46bef598",
            "locker": "src",
            "key": "...base64 data...",
            "data": "...base64 data..."
        }]
    }

The `activate` array contains one entry for each Activation Key produced for the given
user. Each key is given an `id` and contains the locker's `key` encrypted with the user's
public key. The `data` contains the entitlement for the Activation Key.

## Generating Activation Keys

To authorize a package for a user, you must have their public key (i.e., the `"cert.json"`
file in their local package repository). Further, the public key of the current local
package repository must be listed in the `trust` array for the package. If that is the
case and the `"cert.json"` is available, this command will generate an Activation Key for
the package:

    sencha package authorize some-package@1.1.0 cert.json

This produces an Activation Key for this user at version 1.1.0 of "some-package". This
generates the Activation Key file in this local repository as described above.

### Multiple Lockers

If the package contains multiple `locker` instances, the only process effected is the
generation of Activation Keys since these are per-locker. When there is only one `locker`,
the above command defaults to generating an Activation Key for that `locker`. When there
are multiple, the above command produces an error instead. This is because you must
specify the name of the `locker` you want to activate or the `--all` switch.

The following activates all lockers for the user:

    sencha package authorize --all some-package@1.1.0 cert.json

The following activates just the "src" locker for the user:

    sencha package authorize --locker=src some-package@1.1.0 cert.json

### Distributing Activation Keys

Sencha Cmd automatically looks for an Activation Key file when it downloads packages
that have a `locker` property so this file is typically transparent to the package user.
If the package has already been downloaded and the Activation Key is generated later, then
the user will need to run this command to update their keys:

    sencha app refresh --packages
