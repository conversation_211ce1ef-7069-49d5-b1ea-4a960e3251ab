# Using Sencha Cmd with Legacy Ext JS 4.1

This guide walks through the process of using [Sencha Cmd](http://www.sencha.com/products/sencha-cmd/)
with Ext JS 4.1 applications starting with the `sencha generate app` command and ending
with a running application.

The process for upgrading an existing (non-Sencha Cmd) application to conform to the
expectations of Sencha Cmd is covered at the end of this guide. It is important to first
understand the "ideal" or "default" structure as a point of comparison. The differences
between this default structure and the structure of an existing application are what
drive the upgrade process for existing applications.

## Prerequisites

The following guides are recommended reading before proceeding further:

  - [Introduction to Sencha Cmd](#!/guide/command).

## Differences with Ext JS 4.2 and Later

The primary difference between Ext JS 4.1 applications and Ext JS 4.2 and later applications is how
Sencha Cmd automates themes for these frameworks. In Ext JS 4.1 applications, themes are
managed by Cmd as part of the application. In Ext JS 4.2 and higher, themes have moved in
to Packages. This guide describes how "application themes" are managed by Sencha Cmd for
Ext JS 4.1 applications. For details on Ext JS 4.2 application and "theme packages" please
see [Using Sencha Cmd with Ext JS](#!/guide/command_app).

## Generating Your Application

Our starting point is to generate an application skeleton. This is done using
the following command:

    sencha generate app MyApp /path/to/MyApp

**Important.** The above command must be able to determine the appropriate SDK. This can
be satisfied by either executing this command from a folder containing an extracted SDK
distribution or by using the `-sdk` switch like so:

    sencha -sdk /path/to/SDK generate app MyApp /path/to/MyApp

The application files generated by this command should have the following structure:

    .sencha/                    # Sencha-specific files (for example, configuration)
        app/                    # Application-specific content
            sencha.cfg          # Application configuration file for Sencha Cmd
            build-impl.xml      # Standard application build script
            plugin.xml          # Application-level plugin for Sencha Cmd
        workspace/              # Workspace-specific content (see below)
            sencha.cfg          # Workspace configuration file for Sencha Cmd
            plugin.xml          # Workspace-level plugin for Sencha Cmd

    ext/                        # A copy of the Ext JS SDK
        cmd/                    # Framework-specific content for Sencha Cmd
            sencha.cfg          # Framework configuration file for Sencha Cmd
        src/                    # The Ext JS source
        ext-*.js                # Pre-compiled and bootstrap files
        ...

    index.html                  # The entry point to your application
    app.json                    # Application descriptor
    app.js                      # Your application's initialization logic
    app/                        # Your application's source code in MVC structure
        model/                  # Folder for application model classes.
        store/                  # Folder for application stores
        view/                   # Folder for application view classes.
            Main.js             # The initial default View
        controller/             # Folder for application controllers.
            Main.js             # The initial default Controller

    packages/                   # Sencha Cmd packages and application themes
        default/                # The name of the theme ("default" is generated initially)
            images/             # Custom images for the theme
            sass/
                app.scss        # Compiles to ./resources/default/app.css
                config.rb       # Compass config file for the theme
            slice-src/          # Source code to process image slicing
                custom.js       # Contains any theme customizations (like custom components)
                manifest.js     # Generated (do not edit)
                render.js       # Generated (do not edit)
                shortcuts.js    # Generated (do not edit)
            theme.html          # The control file for generating image slices

    resources/
        default/
            images/             # Target location for image slices and custom images
            styles.css          # Generated from ./packages/default/sass/app.scss

    build/                      # The folder where build output is placed.

There is no distinction between workspace and app content in a single-page application.
This distinction becomes important for multi-page applications as described in
[Multi-page and Mixed Apps](#!/guide/command_app_multi).

## Building Your Application

All that is required to build your application is to run the following command:

    sencha app build

**Important.** In order to execute this command, the current directory **must** be the
top-level folder of your application (in this case, `"/path/to/MyApp"`).

This command will build your markup page, JavaScript code, SASS and themes into the `"build"`
folder.

## Sencha Cmd Web Server

The Sencha Cmd web server lets you serve files from your applications directory. 
Use this command to start the web server:

    sencha web start

This command uses default port 1841 and the current directory.
The web server runs until stopped with Ctrl+C or from another window with:

    sencha web stop

To access the Sencha Cmd web server, use: 

    http://localhost:1841/

You can use the `-port` option to specify another port, and you can use the `-map`
option to specify the path to another directory.

## Extending Your Application

The `sencha generate` command helps you quickly generate common MVC components such as
controllers or models:

    sencha help generate

You should see this:

    Sencha Cmd vX.Y.Z.nnn
    sencha generate

    This category contains code generators used to generate applications as well
    as add new classes to the application.

    Commands
      * app - Generates a starter application
      * controller - Generates a Controller for the current application
      * form - Generates a Form for the current application (Sencha Touch Specific)
      * model - Generates a Model for the current application
      * package - Generates a starter package
      * profile - Generates a Profile for the current application (Sencha Touch Specific)
      * theme - Generates a theme page for slice operations (Ext JS Specific)
      * view - Generates a View for the current application (Ext JS Specific)
      * workspace - Initializes a multi-app workspace

**Important.** In order to execute the commands discussed below, the current directory on
the console **must** be inside your application (in this case, "/path/to/MyApp").

### Adding New Models

Adding a model to your application is done by making the `"/path/to/MyApp"` your current
directory and running Sencha Cmd, like this:

    cd /path/to/MyApp
    sencha generate model User id:int,name,email

This command adds a model to the application called `User` with the given 3 fields.

**Note.** This is the only `generate` command that is compatible with a
[Sencha Architect](http://www.sencha.com/products/architect/) project. The typical use of
this command is to automate or script the creation of data models in Sencha Architect.

### Adding New Controllers

Adding a controller is similar to adding a model:

    cd /path/to/MyApp
    sencha generate controller Central

There are no other parameters in this case beyond the controller name.

**Note.** This command is *not* compatible with a
[Sencha Architect](http://www.sencha.com/products/architect/) project.

### Adding New Views

Adding a view to your application is also similar:

    cd /path/to/MyApp
    sencha generate view SomeView

There are no other parameters in this case beyond the view name.

**Note.** This command is *not* compatible with a
[Sencha Architect](http://www.sencha.com/products/architect/) project.

## Custom Themes

All applications start with a default theme, so it is typically not necessary to add any
themes. To enable an application to support multiple themes, the first step is to use the
following:

    cd /path/to/MyApp
    sencha generate theme red

This creates the following folders and some starter content:

    red/                    # The name of the theme
        sass/
            app.scss        # Compiles to ./resources/red/app.css
            config.rb       # Compass config file for the theme
        slice-src/          # Source code to process image slicing
            custom.js       # Contains any theme customizations (like custom components)
            manifest.js     # Generated (do not edit)
            render.js       # Generated (do not edit)
            shortcuts.js    # Generated (do not edit)
        theme.html          # The control file for generating image slices

For details on how to manage and build themes, see 
[Building Themes](#!/guide/command_theme).

## Customizing The Build

There are various configuration options available in the `".sencha/app/sencha.cfg"` file. In
the case of a single-page application, it is best to ignore the `".sencha/workspace"`
folder, which also has a config file.

### The classpath

The `sencha app build` command knows where to find the source of your application due to
the `app.classpath` configuration value stored in `".sencha/app/sencha.cfg"`. By default,
this value is:

    app.classpath=${app.dir}/app,${app.dir}/app.js

Adding directories to this comma-separated list informs the compiler where to find the
source code required to build the application.

### Further Reading

To learn more about the build process provided by Sencha Cmd, please refer to
[Inside The App Build Process](#!/guide/command_app_build).

## Upgrading Your Application

Generate applications include two basic kinds of content relevant to Sencha Cmd: build
scripts (or scaffolding) and the important content of the used Sencha SDK's. As such, you
will occasionally need to upgrade these pieces. You can do this with the following
command:

    sencha app upgrade [ path-to-new-framework ]

The "path-to-new-framework" is optional and is used to upgrade both the Sencha Cmd
scaffold *and* the framework used by the application.

For details on the upgrade process, see [Understanding App Upgrade](#!/guide/command_app_upgrade).

## Existing Applications

The key pieces of scaffold produced by Sencha Cmd are these:

    .sencha/
    app.json
    build.xml
    index.html

The first three of these can be simply copied from an application generated to a temp
folder. An existing application will typically have some markup entry page and if this is
not `"index.html"`, you can add the following to your `"app.json"`:

    {
        ...

        "indexHtmlPath": "index.php"
    }

Naturally, the value should be whatever is correct for the application. In order for the
generated build script to understand this markup file, however, the file should contain
the standard boilerplate found in the generated `"index.html"`:

    <!-- <x-compile> -->
        <!-- <x-bootstrap> -->
            <link rel="stylesheet" href="bootstrap.css">
            <script src="ext/ext-dev.js"></script>
            <script src="bootstrap.js"></script>
        <!-- </x-bootstrap> -->

        <script src="app.js"></script>
    <!-- </x-compile> -->

The exact contents of the `script` tags may vary.

### Configuration

There are probably several pieces of an existing application that will not match with the
default structure of a Sencha Cmd application. At this point, there are two paths to
consider:

  * Restructure the application to conform to the generated structure
  * Configure the build process to match the application structure

For details on how to configuring the build process, consult
[Inside The App Build Process](#!/guide/command_app_build).

### Alternatives

If an existing application cannot be made to conform with the expectations of the build
script (either by restructuring the app or configuring the build), Sencha Cmd still has
useful functionality accessible via low-level commands.

To implement a build process similar to that provided by Sencha Cmd (that is, for a single
page), see the [Single-Page Ext JS Apps](#!/guide/command_app_single) guide.

For more complex scenarios, possibly involving multiple pages, see the
[Multi-Page and Mixed Apps](#!/guide/command_app_multi) guide.

## Next Steps

 - [Compiler-Friendly Code Guidelines](#!/guide/command_code)
 - [Sencha Cmd Packages](#!/guide/command_packages)
 - [Workspaces in Sencha Cmd](#!/guide/command_workspace)
 - [Understanding App Upgrade](#!/guide/command_app_upgrade)
 - [Inside The App Build Process](#!/guide/command_app_build)
 - [Advanced Sencha Cmd](#!/guide/command_advanced)
