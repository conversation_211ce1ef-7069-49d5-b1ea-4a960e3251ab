# Building Themes for Legacy Ext JS 4.1

This guide describes the file-level mechanics of building and managing themes with
Sencha Cmd, which can automate several of the steps required to build a theme. Note
that developing themes or managing styles or Sass variables are not covered here -- these
are part of your application development process.

The following guides are recommended reading before proceeding further:

  - [Introduction to Sencha Cmd](#!/guide/command).
  - [Using Sencha Cmd](#!/guide/command_app).

## Getting Started

First, in order to build themes from Sass, be sure to install [Ruby](http://www.ruby-lang.org/en/downloads/).
On Windows, [Ruby Installer](http://rubyinstaller.org/) is the simplest way to get Ruby
installed. On newer versions of Mac OS X, Ruby comes pre-installed.

**Note** Sencha Cmd supports Ruby 1.9.3. This is because Sencha Cmd includes a version of
[Compass](http://compass-style.org/) (and Sass) that are compatible with Sencha frameworks
and these are not compatible with Ruby 2.0

Most of this guide assumes you are working in an application generated by Sencha Cmd. If
that is not the case, many of these steps will change to reflect different paths and the
like. See the section below on "Managing Themes Manually".

### Use of Sass

The majority of your work on themes is done in Sass. The starter files are generated for
you, and you edit them to complete your theme. When you compile your Sass, it should generate
a CSS file in `"./.res/default"` named `"app.css"`.

## Multiple Themes

If you need two or more themes (as described in [Using Sencha Cmd with Ext JS](#!/guide/command_app)),
you can generate new themes with this command:

    sencha generate theme foo

This will generate the following:

 - `./resources/theme/foo/sass`

The SASS should be compiled to `"./.res/foo/app.css"`.

## Anatomy of a Theme

The themes of an Ext JS 4.1 application are stored in the application's `"packages"` folder
as follows:

    packages/                   # The container of Packages and Ext JS 4.1 themes
        default/                # The name of the theme ("default" is generated initially)
            images/             # Custom images for the theme
            sass/
                app.scss        # Compiles to ./resources/default/app.css
                config.rb       # Compass config file for the theme
            slice-src/          # Source code to process image slicing
                custom.js       # Contains any theme customizations (like custom components)
                manifest.js     # Generated (do not edit)
                render.js       # Generated (do not edit)
                shortcuts.js    # Generated (do not edit)
            theme.html          # The control file for generating image slices

### Packages vs. Application Theme

For a single-application workspace, this `"packages"` folder coincides with the `"packages"`
folder that contains workspace's [Sencha Cmd Packages](#!/guide/command_packages). While
these are similar to these "full" Packages, they are not exactly the same. In a multi-app
workspace, these folders do not coincide. In fact, they look more like this:

    myworkspace/
        packages/               # Home for Sencha Cmd Packages in the workspace
        app1/                   # App 1
            packages/           # Themes for App 1
        app2/                   # App 2
            packages/           # Themes for App 2

## Building an Application's Themes

The `sencha app build` command will produce full image sets for all themes in a given 
application as part of the standard build process. To achieve this, images from the 
base framework theme are first staged into the `"resources/<theme name>/images"` directory
under the application's root directory.  Then, sliced images for each theme are generated 
using the technique described in the following section and overlaid over the theme images 
provided by the framework. Finally, user supplied images located in the application's 
`"resources/images"` directory are overlaid into the output of the theme build operations
to produce the final image set.  The resultant application resource directory will have the
following structure:

    resources/
        images/                 # Theme-independent image assets
        default/
            images/             # Target location for image slices and custom images
            styles.css          # Generated from ./packages/default/sass/app.scss

Images are staged into `"resources/<theme name>/images"` in the application's root folder
to enable development-time viewing of a built theme. The `"resources"` directory is then
copied to the application's `"build"` folder to maintain relative paths for deployment.
Since the `"resources/<theme name>"` directory is generated to a file system location
commonly under revision control, you can elect to commit these files or mark them as
"ignored" (such as a .gitignore entry).

## Building Just One Theme

Typically, your themes will be compiled and built by the `sencha app build` command, but
it is sometimes helpful to produce images for a single theme at a time. To compile a theme, 
use the `compass compile` command in your Sass directory.

    cd /path/to/MyApp/resources/sass/default
    compass compile

This should produce the CSS in `"../../../../resources/default/app.css"` (or from the root of your
application folder, `".res/default/app.css"`).

The next step is to build the image slices needed for IE browsers. This is done with the
following command:

    cd /path/to/MyApp
    sencha theme build -out resources/default/images

This will place the sliced images in the `"./.res/default/images"` folder alongside the
CSS file generated by Sass. If the '-out' parameter is omitted, then sliced images will be placed
in a 'resources/images/default' directory under the default build output folder for the 
application. All image paths are relative to the CSS file location to minimize the size 
of the CSS file.






















## Anatomy of a Theme

The `theme` folder is split in to two levels. For clarity, lets consider a `theme` folder
with two themes:

    theme/
        custom.js
        manifest.js
        render.js
        shortcuts.js
        default/
            theme.html
        gray/
            theme.html

### theme.html

The `"theme.html"` file is the entry point for the image generation phase of the theme
builder.

You should be able to open this file in Chrome or any other modern browser and see what the
base image will be for your theme. This is an ideal page to test your styling because every
component in every state will be present.

This is an example of what the `"resources/theme/default/theme.html"` file looks like:

    <html>
        <head>
            <title></title>
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

            <link rel="stylesheet" type="text/css" href="../../css/default/theme.css" />
            <link rel="stylesheet" type="text/css" href="../styles.css" />

            <script type="text/javascript" src="../../../sdk/ext-all.js"></script>
            <script type="text/javascript" src="../manifest.js"></script>
            <script type="text/javascript" src="../shortcuts.js"></script>
            <script type="text/javascript" src="../custom.js"></script>
            <script type="text/javascript" src="../render.js"></script>
        </head>
        <body class="ext-generator">
        </body>
    </html>

This file is designed to be edited as needed. This file will not be edited or regenerated
by Sencha Cmd.

**Important.** While this file is freely editable, be sure to keep the relative order of
the `link` and `script` tags. In particular `"custom.js"` must come after `"manifest.js"` and
`"shortcuts.js"` and before `"render.js"`.

The most likely things you might need to edit in this file are the relative paths. All of
the generated files included in the page need to be preserved. You can add `script` tags
or `link` tags as needed, but consider their order in the sequence of tags.

### Private Files

The following files should be considered private and should not be edited:

 - `manifest.js` - The set of all components to render.
 - `shortcuts.js` - Reusable "templates" used by the manifest.
 - `styles.css` - Styles specific to the theme builder.
 - `render.js` - Renders the components described by the manifest and shortcuts.

### Customization

Add value to the process through the `"custom.js"` file. The primary use of this file is
to add new shortcuts or manifest entries that might be needed by custom components you
have created for your application. This file will not be regenerated by Sencha Cmd.

### Third Party Components

If you have received custom components from a third party, you should ask them to provide
a custom theme definition file so that you can include it in your `theme` folder and as a
`script` tag in your '"theme.html"'. The content of such a file should be the same as
described for a `"custom.js"` file.

You will probably also need SASS files (or at least CSS files) to add to your SASS theme
and/or as `link` tags in `"theme.html"`.

## Managing Themes Manually

Many applications existed before Sencha Cmd provided application management features or
for other reasons do not have the structure assumed by the simple commands described
here. While the commands to handle this case require more switches and understanding, it
is possible to use Sencha Cmd in these more complex situations.

**Important.** The new version of Sencha Cmd takes a different approach to theme building
than did previous versions (which called this mechanism the "slicer"). There is no longer
a `sencha slice` command. This is now handled by the new `sencha theme build` command.

### Creating Your Custom Theme

In order to produce sliced images for your theme, you need the necessary files that define
the theme. The easiest way to get these files is to generate an application in some temporary
folder and copy the `"./resources/theme"` folder to a location of your choosing using the
following:

    sencha generate app /temp/dummyApp

Then copy `"/temp/dummApp/resources/theme"` to your desired location. For example, let's say
that location is `"/path/to/MyApp/stuff/theme"`.

Following the above rules for customization of `"theme.html"` you can flatten the folders in
to a single folder and change the relative paths in `"theme.html"` accordingly. You can also
correct the relative paths to your CSS file(s) and the framework.

## Compiling Your Custom Theme

Once you have moved the theme files to `"/path/to/MyApp/stuff/theme"` and editing all of the
relative paths in `"theme.html"`, you can proceed to generate images. Let's assume you run
the command from the root of your application.

    cd /path/to/MyApp
    sencha theme build -page stuff/theme/theme.html -out images/theme

This will load `"stuff/theme/theme.html"` using an embedded WebKit instance and capture an
image of the content of the rendered page as well as a data object describing all of the
image slicing required. From those two results, the image slicing will proceed to generate
all of the images to the `"images/theme"` folder.

If something is wrong with the generated images, it can be helpful to keep the page image
and the data object for diagnostic purposes. This can be done by specifying their names in
the command:

    cd /path/to/MyApp
    sencha theme build -page stuff/theme/theme.html -out images/theme \
        -image image.png -data data.json

The image is always a PNG file because PNG is both lossless and has a proper alpha channel.
The data object is always in JSON format.
