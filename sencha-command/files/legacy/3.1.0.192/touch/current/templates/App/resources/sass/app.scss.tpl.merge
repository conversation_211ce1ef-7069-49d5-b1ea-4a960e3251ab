// This following gives you access to the Sencha Touch base mixins, such as `icon`
// Generally, you should never remove this line. It does not actually add any
// CSS, unless of course you call a mixin.
@import 'sencha-touch/base';

// The following two lines import the default Sencha Touch theme. If you are building 
// a new theme, remove them and the add your own CSS on top of the base CSS (which
// is already included in your app.json file).
@import 'sencha-touch/default';
@import 'sencha-touch/default/all';

// Custom code goes here..

// Examples of using the icon mixin:
// @include icon('user');