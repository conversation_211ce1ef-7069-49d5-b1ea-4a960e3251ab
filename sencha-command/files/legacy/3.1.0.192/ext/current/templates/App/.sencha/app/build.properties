# ===========================================
# Do not edit this file.  
# 
# This file defines properties used by 
# build-impl.xml, which is the base impl
# of an applications build process
# All modifications
# should be made to build.xml and / or 
# local.properties in the application's 
# root directory.
# ===========================================


# ===========================================
# various js / compiler properties
# ===========================================

args.environment=production
build.dir=${app.build.dir}/${args.environment}

build.compression.production=-yui
build.compression.testing=


# These properties can be modified to change general build options
# such as excluding files from the set.  The format expects newlines
# for each argument, for example:
#            
#   build.operations.production=\
#       exclude\n \
#       -namespace=Ext\n
build.operations.production=
build.operations.testing=

build.options.production=-debug\=false
build.options.testing=
build.resources.dir=${build.dir}/resources
build.images.dir=${build.resources.dir}/images

# This property can be modified to change the input and output page file 
# used in the compile command. (eg: index.aspx, index.jsp ... )
app.page.name=index.html

# the input page file
app.page.file=${app.dir}/${app.page.name}
# the outpout page file
build.page.file=${build.dir}/${app.page.name}

# the output js file
build.classes.name=all-classes.js
build.classes.file=${build.dir}/${build.classes.name}

# ===========================================
# various sass / css properties
# ===========================================

app.out.base=${app.name}-all
app.out.base.debug=${app.out.base}-debug

app.out.scss=${build.dir}/${app.out.base.debug}.scss

app.out.css.rel=resources/${app.out.base.debug}.css
app.out.css=${build.dir}/${app.out.css.rel}
app.out.css.compressed=${build.dir}/resources/${app.out.base}.css

compass.sass.dir=${build.dir}
compass.css.dir=${build.dir}/resources
compass.config.file=${app.dir}/sass/config.rb
compass.compile.options=--trace --boring --force

app.example.dir=${app.dir}/sass/example
app.example.base=${app.name}-example
app.example.css.rel=resources/${app.example.base}.css
app.example.css=${build.dir}/${app.example.css.rel}
app.example.scss=${build.dir}/${app.example.base}.scss

# Options to pass to the "sencha fs slice" command.
build.slice.options=
