<?xml version="1.0" encoding="utf-8"?>
<project name="{pkgName}" default=".help">
    <!--
    The build-impl.xml file imported here contains the guts of the build process. It is
    a great idea to read that file to understand how the process works, but it is best to
    limit your changes to this file.
    -->
    <import file="$\u007Bbasedir}/.sencha/package/build-impl.xml"/>

    <!--
    The following targets can be provided to inject logic before and/or after key steps
    of the build process:

        The "init-local" target is used to initialize properties that may be personalized
        for the local machine.

            <target name="-before-init-local"/>
            <target name="-after-init-local"/>

        The "clean" target is used to clean build output from the build.dir.

            <target name="-before-clean"/>
            <target name="-after-clean"/>

        The general "init" target is used to initialize all other properties, including
        those provided by Sencha Cmd.

            <target name="-before-init"/>
            <target name="-after-init"/>
        
        The "build" target performs the call to Sencha Cmd to build the application.

            <target name="-before-build"/>
            <target name="-after-build"/>
    -->

</project>
