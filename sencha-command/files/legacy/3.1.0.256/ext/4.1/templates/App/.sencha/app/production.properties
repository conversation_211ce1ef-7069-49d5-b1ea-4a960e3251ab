# =============================================================================
# This file defines properties used by build-impl.xml, which is the core of
# the applications build process. You can customize this file to control the
# various options used in the production build.
#
# IMPORTANT - Sencha Cmd will merge your changes with its own during upgrades.
# To avoid potential merge conflicts avoid making large, sweeping changes to
# this file.
# =============================================================================

# ===========================================
# various js / compiler properties
# ===========================================

build.compression.yui=1
build.compression.closure=0
build.compression.uglify=0

build.options.debug=debug:false

# This properties can be modified to tune the optimization process
build.optimize=\
        optimize\n \
            -define-rewrite\n

build.css.preprocess=true                                                                 
build.css.preprocessor.opts=
build.css.compress=true
