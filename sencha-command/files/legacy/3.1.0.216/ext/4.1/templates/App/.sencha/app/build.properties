# =============================================================================
# This file defines properties used by build-impl.xml, which is the core of
# the applications build process. You can customize this file to control the
# various options used in the build.
#
# IMPORTANT - Sencha Cmd will merge your changes with its own during upgrades.
# To avoid potential merge conflicts avoid making large, sweeping changes to
# this file.
# =============================================================================

# ===========================================
# various js / compiler properties
# ===========================================

# Specify the name for the individual resource dirs in the app
app.sass.name=sass
app.css.name=css
app.theme.name=theme
app.img.name=images

# The entry point for the theme command
theme.page.name=theme.html

app.resources.dir=${app.dir}/resources

args.environment=production

build.dir=${app.build.dir}/${args.environment}

# the output js file
build.classes.name=all-classes.js
build.classes.file=${build.dir}/${build.classes.name}

# the output page file
build.page.file=${build.dir}/${app.page.name}

# Specify the resources path in the build
build.resources.dir=${build.dir}/resources

# Specify the images path in the app
build.images.dir=${build.resources.dir}/${app.img.name}

# Specify the sass path in the build
build.css.dir=${build.resources.dir}/${app.sass.name}

# Specify the files to exclude when copying resources to the build
build.resources.exclude=${app.theme.name}/**/*

build.capture.png=${build.dir}/theme-capture.png
build.capture.json=${build.dir}/theme-capture.json

build.options=${build.options.debug}

# ===========================================
# various sass / css properties
# ===========================================

app.out.base=${app.name}-all
app.out.base.debug=${app.out.base}

# Specify the resources path in the app
app.packages.dir=${app.dir}/packages

# Specify the theme path in the app
app.theme.dir=${app.packages.dir}

# Specify the sass path in the app
app.sass.dir=${app.theme.dir}/${theme.name}/${app.sass.name}

enable.theme.slicer=true
