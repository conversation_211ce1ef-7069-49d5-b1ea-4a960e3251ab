<?xml version="1.0" encoding="utf-8"?>
<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project>
    <!--
    Init-Local
    -->
    <target name="-before-init-local"/>
    <target name="-init-local">
        <property file="${basedir}/../../local.properties"/>

        <script language="javascript">
            var f = new java.io.File(project.getProperty("basedir"));
            var sub = ".sencha/workspace/sencha.cfg";

            for (var p = f; p; p = p.getParentFile()) {
                var t = new java.io.File(p, sub);
                if (t.exists()) {
                    // we found the workspace folder!

                    t = new java.io.File(p, "local.properties");
                    if (t.exists()) {
                        var loader = project.createTask("property");
                        loader.setFile(new java.io.File(t.getCanonicalPath()));
                        loader.execute();
                    }

                    break;
                }
            }
        </script>

    </target>
    <target name="-after-init-local"/>
    <target name="init-local"
            depends="-before-init-local,-init-local,-after-init-local"/>

    <!--
    Find Sencha Cmd
    -->
    <target name="find-cmd" unless="cmd.dir">
        <!--
        Run "sencha which" to find the Sencha Cmd basedir and get "cmd.dir" setup. We
        need to execute the command with curdir set properly for Cmd to pick up that we
        are running for an application.
        -->
        <exec executable="sencha" dir="${basedir}">
            <arg value="which"/><arg value="-o=$cmddir$"/>
        </exec>

        <!-- Now read the generated properties file and delete it -->
        <property file="$cmddir$"/>
        <delete file="$cmddir$"/>

        <echo>Using Sencha Cmd from ${cmd.dir}</echo>
    </target>

    <!--
    Init
    -->
    <target name="-before-init"/>
    <target name="-init">
        <taskdef resource="com/sencha/ant/antlib.xml" classpath="${cmd.dir}/sencha.jar"/>
        <x-sencha-init prefix=""/>
        <property file="${app.config.dir}/build.properties"/>

        <if>
            <equals arg1="${args.environment}" arg2="production"/>
            <then>
                <property name="build.css.compress" value="${build.css.compress.production}"/>
                <property name="build.css.preprocess" value="${build.css.preprocess.production}"/>
                <property name="build.css.preprocessor.opts" value="${build.css.preprocessor.opts.production}"/>
                <property name="build.compression" value="${build.compression.production}"/>
                <property name="build.operations" value="${build.operations.production}"/>
                <property name="build.options" value="${build.options.production}"/>
            </then>
            <else>
                <if>
                    <equals arg1="${args.environment}" arg2="testing"/>
                    <then>
                        <property name="build.css.compress" value="${build.css.compress.testing}"/>
                        <property name="build.css.preprocess" value="${build.css.preprocess.testing}"/>
                        <property name="build.css.preprocessor.opts" value="${build.css.preprocessor.opts.testing}"/>
                        <property name="build.compression" value="${build.compression.testing}"/>
                        <property name="build.operations" value="${build.operations.testing}"/>
                        <property name="build.options" value="${build.options.testing}"/>
                    </then>
                    <else>
                        <fail>The ExtJS SDK currently supports 'production' and 'testing' builds not ${args.environment}</fail>
                    </else>
                </if>
            </else>
        </if>
        
        <!-- Some operations require sencha.jar in the Ant classpath -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
        
        <x-verify-app-cmd-ver/>
       
    </target>
    <target name="-after-init"/>
    <target name="init"
            depends="init-local,find-cmd,-before-init,-init,-after-init"/>

    <!--
    Clean
    -->
    <target name="-before-clean"/>
    <target name="-clean">
        <delete dir="${build.dir}"/>
    </target>
    <target name="-after-clean"/>
    <target name="clean"
            depends="init,-before-clean,-clean,-after-clean"
            description="Removes all build output produced by the 'build' target"/>

    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-sass">
        <x-compass-compile
            dir="${build.dir}"
            trace="true"
            boring="true"
            force="true"
            sassdir="${compass.sass.dir}"
            cssdir="${compass.css.dir}"
            config="${compass.config.file}"/>

        <if>
            <equals arg1="true" arg2="${build.css.preprocess}"/>
            <then>
                <x-css-preprocess 
                    file="${app.out.css}" 
                    tofile="${app.out.css}" 
                    options="${build.css.preprocessor.opts}"/>
            </then>
        </if>

        <if>
            <equals arg1="true" arg2="${build.css.compress}"/>
            <then>
                <echo>Compressing ${app.out.css} to ${app.out.css.compressed}</echo>
                <x-compress-css srcfile="${app.out.css}"
                                outfile="${app.out.css.compressed}"/>
            </then>
        </if>
       
        <!--
        app.out.css.path is relative to the app output index.html file
        -->
        <x-get-relative-path
            from="${app.dir}"
            to="${app.out.css}"
            property="app.out.css.path"
            />

        <x-get-relative-path
            from="${app.example.dir}"
            to="${app.example.css}"
            property="app.example.css.path"
            />
        
<!--update the application's bootstrap.css file to point to the build output-->
<echo file="${app.dir}/bootstrap.css">
/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It will
 * redirect to the most recently built css file for the application to
 * support development time inspection of css output.
 */
@import '${app.out.css.path}';
</echo>

<!--update the app's example to point to the build output-->
<echo file="${app.example.dir}/example.css">
/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It will
 * redirect to the most recently built example css file for the application to
 * support capture of theme background, frame, and corner images for non-CSS3 
 * browsers.
 */
@import '${app.example.css.path}';
</echo>
                
    </target>
    <target name="-after-sass"/>
    <target name="sass" depends="init" unless="skip.sass"
            description="Builds only the SASS files using Compass">
        <antcall target="-before-sass"/>
        <antcall target="-sass"/>
        <antcall target="-after-sass"/>
    </target>

    <!--
    Inherit Resources
    -->
    <target name="-before-inherit-resources"/>
    <target name="-after-inherit-resources"/>
    <target name="inherit-resources" depends="init"
            description="Performs the resource folder inheritance from base theme(s)">
        <antcall target="-before-inherit-resources"/>

        <if>
            <isset property="theme.base.names"/>
            <then>
                <for list="${theme.base.names}" param="base">
                    <sequential>
                        <echo>Merging resources from base package @{base}</echo>
                        <local name="base.path"/>
                        <local name="base.resource.path"/>
                        <property name="base.path" location="${workspace.packages.dir}/@{base}"/>
                        <property name="base.resource.path" location="${base.path}/build/resources"/>
                        
                        <if>
                            <available file="${base.resource.path}" type="dir" />
                            <then>
                                <copy todir="${build.dir}/resources/" overwrite="true">
                                    <fileset 
                                        dir="${base.resource.path}" 
                                        includes="**/*"
                                        excludes="${base}-all*.css"/>
                                </copy>
                            </then>
                        </if>
                    </sequential>
                </for>
            </then>
        </if>

        <if>
            <isset property="app.requires.names"/>
            <then>
                <for list="${app.requires.names}" param="base">
                    <sequential>
                        <echo>Copying resources from required package @{base}</echo>
                        <local name="base.path"/>
                        <local name="base.resource.path"/>
                        <property name="base.path" location="${workspace.packages.dir}/@{base}"/>
                        <property name="base.resource.path" location="${base.path}/build/resources"/>
                        
                        <if>
                            <available file="${base.resource.path}" type="dir" />
                            <then>
                                <copy todir="${build.dir}/resources/${base}" overwrite="true">
                                    <fileset 
                                        dir="${base.resource.path}" 
                                        includes="**/*"
                                        excludes="${base}-all*.css"/>
                                </copy>
                            </then>
                        </if>
                    </sequential>
                </for>
            </then>
        </if>

        <antcall target="-after-inherit-resources"/>
    </target>
    
    <!--
    Slice
    -->
    <target name="-before-slice"/>
    <target name="-after-slice"/>
    <target name="slice" depends="init" unless="skip.slice"
            description="Slices CSS3 theme to produce non-CSS3 images and sprites">
        <antcall target="-before-slice"/>

        <echo>Capture theme image to ${build.dir}/theme-capture.png</echo>
        <x-sencha-command>
            theme
                capture
                    -page=${app.example.dir}/theme.html
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
        </x-sencha-command>

        <echo>Slicing theme images to ${build.images.dir}</echo>
        <x-sencha-command>
            fs
                slice
                    ${build.slice.options}
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
                    -out=${build.images.dir}
        </x-sencha-command>

        <antcall target="-after-slice"/>
    </target>
    
    <!--
    Copy Resources
    -->
    <target name="-before-copy-resources"/>
    <target name="-after-copy-resources"/>
    <target name="copy-resources" depends="init" unless="skip.slice"
            description="Copy theme resources to folder">
        <antcall target="-before-copy-resources"/>

        <copy todir="${build.dir}/resources" overwrite="true">
            <fileset 
                dir="${app.dir}/resources" 
                includes="**/*" 
                excludes="**/Readme.md"/>
        </copy>
        
        <antcall target="-after-copy-resources"/>
    </target>
    
    <!--
    Refresh Individual Theme
    -->
    <target name="-before-refresh-theme"/>
    <target name="-refresh-theme">
        <echo>Executing 'sencha package build' for theme package ${args.themeName}</echo>
        <x-sencha-command 
            dir="${workspace.packages.dir}/${args.themeName}"
            inheritAll="false">
            <property name="cmd.dir" value="${cmd.dir}"/>
            package
            build
        </x-sencha-command>
    </target>
    <target name="-after-refresh-theme"/>
    <target name="refresh-theme" depends="init" unless="skip.theme"
            description="Executes 'sencha package build' for the specified theme package name">
        <antcall target="-before-refresh-theme"/>
        <antcall target="-refresh-theme"/>
        <antcall target="-after-refresh-theme"/>
    </target>

    <!--
    Build Page
    -->
    <target name="-before-page"/>
    <target name="-page">
        <property name="bootstrap.example.js" value="${app.example.dir}/bootstrap.js"/>
        <property name="bootstrap.base.path" value="${app.example.dir}"/>
        <x-normalize-path path="${build.dir}/resources" property="image.search.path"/>

<echo file="${bootstrap.example.js}">
/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It is 
 * provided to support globbing requires, custom xtypes, and other 
 * metadata-driven class system features 
 */
</echo>
        <!--run compile command to generate concatenated output files for js and scss-->
        <x-sencha-command dir="${app.dir}">
            #base build command
            
            compile
                ${build.options}
                page
                    -name=full-page
                    -in=${app.page.file}
                    -out=${build.page.file}
                    -classes=${build.classes.name}
                    -stylesheet=${app.out.css.rel}
                and
                restore
                    full-page     
                and
                ${build.operations}
                and
                save
                    page
            
            #page command
                
                and
                concat
                    ${build.compression}
                    -out=${build.classes.file}

            # bootstrap
                
                and
                union
                    -not
                    -tag=core
                and
                metadata
                    +append
                    --base-path=${bootstrap.base.path}
                    +loader-paths
                    -jsonp=Ext.Loader.addClassPathMappings
                    -out=${bootstrap.example.js}
                and
                exclude
                    -tag=framework
                and
                include
                    -namespace=Ext.ux
                and
                metadata
                    --base-path=${bootstrap.base.path}
                    +alternates
                    +append
                    -out=${bootstrap.example.js}
                and
                metadata
                    --base-path=${bootstrap.base.path}
                    +alias
                    +append
                    -out=${bootstrap.example.js}
            
            # sass
                
                and
                restore
                    page
                and
                #only set variables for used classes eg. $include-class-name
                sass
                    +class-name-vars
                    -variable=$image-search-path:'${image.search.path}'
                    -variable=$theme-name: '${app.theme}' !default
                    -output=${app.out.scss}
                and
                include
                    -all
                and
                sass
                    +class-name-vars
                    +etc
                    +vars
                    +rules
                    -variable=$image-search-path:'${image.search.path}'
                    -variable=$theme-name: '${app.theme}' !default
                    -output=${app.example.scss}
                and
                # include etc and vars from all classes
                sass
                    +etc
                    +vars
                    +append
                    -output=${app.out.scss}
                and
                restore
                    page
                and
                #only include rules from used classes
                sass
                    +rules
                    +append
                    -output=${app.out.scss}
                and
                sass
                    +ruby
                    -output=${app.out.ruby}                
        </x-sencha-command>
    </target>
    <target name="-after-page"/>
    <target name="page" depends="init" unless="skip.page"
            description="Builds only the application's HTML page">
        <antcall target="-before-page"/>
        <antcall target="-page"/>
        <antcall target="-after-page"/>
    </target>


    <!--
    Build
    -->
    <target name="-before-build"/>
    <target name="-build" depends="page,inherit-resources,copy-resources,sass,slice"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the application"/>

    <!--
    environment setters
    -->

    <target name="production">
        <property name="args.environment" value="production"/>
    </target>

    <target name="testing">
        <property name="args.environment" value="testing"/>
    </target>

    <target name="native">
        <property name="args.environment" value="native"/>
    </target>

    <target name="package">
        <property name="args.environment" value="package"/>
    </target>
    
    <!--
    Helpful targets
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <target name=".help" depends="init"
            description="Provides help on the build script">
        <local name="-alltargets"/>
        <x-shell outputproperty="-alltargets">
            ant -f ${ant.file} -p
        </x-shell>

        <!--
        Remove the annoying "Default taret:.help" smashed on the end of the output.
        -->
        <script language="javascript">
            var s = project.getProperty("-alltargets"),
                n = s.indexOf('Default target:');
            //self.log("all=" + n);
            project.setProperty("-alltargets", s.substring(0, n));
        </script>

        <echo><![CDATA[${-alltargets}
This is the main build script for your application.

The following properties can be used to disable certain steps in the build
process.

 * skip.page        Do not build the HTML page.
 * skip.sass        Do not build the SASS.
 * skip.theme       Do not build the theme images.
            
The following properties can be used to modify the build process.
            
 * build.compression.production         Set the compression for a production
                                        build.
 * build.compression.testing            Set the compression for a test build.
                                        (defaults to none)
 * build.compression                    Set the compression for all builds.
                                        (defaults to
                                        build.compression.production
                                        or build.compression.testing)
            
 * build.operations.production          Insert commands into the compile command
                                        for a production build. 
 * build.operations.testing             Insert commands into the compile command
                                        for a testing build.
 * build.operations                     Insert commands into the compile command
                                        for all builds.
                                        (defaults to build.operations.production
                                        or build.operations.testing)
 
 * build.options.production             Set options for a production build.
                                        (eg: -debug=false)
 * build.options.testing                Set options for a testing build.
 * build.options                        Set options for all builds.
                                        (defaults to build.options.production
                                        or build.options.testing)
 
 * app.page.name                        Set the input and output page file
                                        for the compile command.
 
 * build.classes.name                   Specify the compiled js file
                                        

For details about how these options affect your build, see

    ${basedir}/.sencha/app/build-impl.xml

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    ant .props
        ]]></echo>
    </target>

</project>
