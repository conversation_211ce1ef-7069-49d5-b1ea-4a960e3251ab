# -----------------------------------------------------------------------------
# This file contains configuration options that apply to all applications in
# the workspace. By convention, these options start with "workspace." but any
# option can be set here. Options specified in an application's sencha.cfg will
# take priority over those values contained in this file. These options will
# take priority over configuration values in Sencha Cmd or a framework plugin.

# -----------------------------------------------------------------------------
# This configuration property (if set) is included by default in all compile
# commands executed according to this formulation:
#
#   sencha compile -classpath=...,$\u007Bframework.classpath},$\u007Bworkspace.classpath},$\u007Bapp.classpath}

#workspace.classpath=

#------------------------------------------------------------------------------
# This is the folder for build outputs in the workspace

workspace.build.dir=$\u007Bworkspace.dir}/build

#------------------------------------------------------------------------------
# This folder contains all generated and extracted packages.

workspace.packages.dir=$\u007Bworkspace.dir}/packages

# =============================================================================
# Customizations go below this divider to avoid merge conflicts on upgrade
# =============================================================================
