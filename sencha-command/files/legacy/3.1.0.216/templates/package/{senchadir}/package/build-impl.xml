<?xml version="1.0" encoding="utf-8"?>
<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project>
    <!--
    Init-Local
    -->
    <target name="-before-init-local"/>
    <target name="-after-init-local"/>
    <target name="init-local">
        <antcall target="-before-init-local"/>

        <property file="${basedir}/../../local.properties"/>

        <script language="javascript">
            var f = new java.io.File(project.getProperty("basedir"));
            var sub = ".sencha/workspace/sencha.cfg";

            for (var p = f; p; p = p.getParentFile()) {
                var t = new java.io.File(p, sub);
                if (t.exists()) {
                    // we found the workspace folder!

                    t = new java.io.File(p, "local.properties");
                    if (t.exists()) {
                        var loader = project.createTask("property");
                        loader.setFile(new java.io.File(t.getCanonicalPath()));
                        loader.execute();
                    }

                    break;
                }
            }
        </script>

        <antcall target="-after-init-local"/>
    </target>

    <!--
    Find Sencha Cmd
    -->
    <target name="find-cmd" unless="cmd.dir">
        <!--
        Run "sencha which" to find the Sencha Cmd basedir and get "cmd.dir" setup. We
        need to execute the command with curdir set properly for Cmd to pick up that we
        are running for an application.
        -->
        <exec executable="sencha" dir="${basedir}">
            <arg value="which"/><arg value="-o=$cmddir$"/>
        </exec>

        <!-- Now read the generated properties file and delete it -->
        <property file="$cmddir$"/>
        <delete file="$cmddir$"/>

        <echo>Using Sencha Cmd from ${cmd.dir}</echo>
    </target>

    <!--
    Init
    -->
    <target name="-before-init"/>
    <target name="-after-init"/>
    <target name="init" depends="init-local,find-cmd">
        <antcall target="-before-init"/>

        <taskdef resource="com/sencha/ant/antlib.xml" classpath="${cmd.dir}/sencha.jar"/>
        <x-sencha-init prefix=""/>
        
        <!-- default the build environment to production if it is unset by this point -->
        <property name="args.environment" value="production"/>
        
        <!-- initialize the build.dir property from the workspace config after we've loaded it -->
        <property name="build.dir"
                  location="${package.build.dir}"/>

        <property name="build.resources.dir" 
                  location="${build.dir}/resources"/>
        
        <property name="package.resources.dir" 
                  location="${package.dir}/resources"/>
        
        <condition property="package.resources.dir.exists">
            <available file="${package.resources.dir}" type="dir" />
        </condition>
        
        <property name="package.sass.dir" 
                  location="${package.dir}/sass"/>
        
        <condition property="package.sass.dir.exists">
            <available file="${package.sass.dir}" type="dir" />
        </condition>
        
        <property name="build.all.js" 
                  value="${build.dir}/${package.name}.js"/>
        
        <property name="build.all.debug.js" 
                  value="${build.dir}/${package.name}-debug.js"/>
        
        <property name="build.all.dev.js" 
                  value="${build.dir}/${package.name}-dev.js"/>

        <property name="build.all.scss" 
                  value="${build.dir}/${package.name}-all-debug.scss"/>
        
        <property name="build.all.rtl.scss" 
                  value="${build.dir}/${package.name}-all-rtl-debug.scss"/>
        
        <property name="build.all.css.debug" 
                  value="${build.dir}/resources/${package.name}-all-debug.css"/>
        
        <property name="build.all.rtl.css.debug" 
                  value="${build.dir}/resources/${package.name}-all-rtl-debug.css"/>
        
        <property name="build.all.css" 
                  value="${build.dir}/resources/${package.name}-all.css"/>
        
        <property name="build.all.rtl.css" 
                  value="${build.dir}/resources/${package.name}-all-rtl.css"/>
        
        <property name="build.all.ruby"
                  value="${build.dir}/config.rb"/>
        
        <x-normalize-path path="${build.dir}/resources" property="image.search.path"/>

        <if>
            <isset property="package.sass.dir.exists"/>
            <then>
                <property name="build.compile.sass.rtl">
                    sass
                        -etc=true
                        -vars=true
                        -rules=true
                        -class-name-vars=true
                        -variable=$image-search-path:'${image.search.path}'
                        -variable=$theme-name: '${package.name}' !default
                        -output=${build.all.rtl.scss}
                    and
                    sass
                        -ruby=true
                        -output=${build.all.ruby}
                </property>
                <property name="build.compile.sass">
                    sass
                        -etc=true
                        -vars=true
                        -rules=true
                        -class-name-vars=true
                        -variable=$image-search-path:'${image.search.path}'
                        -variable=$theme-name: '${package.name}' !default
                        -output=${build.all.scss}
                    and
                    sass
                        -ruby=true
                        -output=${build.all.ruby}
                </property>
            </then>
        </if>
        
        <property name="build.compile.sass.rtl" value=""/>
        <property name="build.compile.sass" value=""/>
        
        <!--
        Options to pass to the "sencha fs slice" command.
        -->
        <property name="build.slice.options" value=""/>

        <property name="pkg.build.dir" location="${workspace.build.dir}/${package.name}"/>
        <property name="pkg.file.name" value="${package.name}.pkg"/>
        <property name="pkg.includes" value="**/*"/>
        <property name="pkg.excludes" value="package.json"/>

        <!-- Some operations require sencha.jar in the Ant classpath -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
        
        <!--<x-verify-app-cmd-ver/>-->

        <x-script-def name="x-load-json-cfg">
            <script src="${cmd.dir}/ant/JSON.js"/>
            <script src="${cmd.dir}/ant/ant-util.js"/>
            importPackage(com.sencha.util);
            importPackage(java.io);
            
            var packageDir = project.getProperty("package.dir"),
                packageCfg = JSON.parse(
                    FileUtil.readUnicodeFile(new File(packageDir, "package.json")));

            project.setProperty("package.version", packageCfg.version);
        </x-script-def>
        
        <x-load-json-cfg/>

        <if>
            <not>
                <isset property="package.framework"/>
            </not>
            <then>
                <property name="skip.style" value="1"/>
            </then>
        </if>
        
        
        <if>
            <isset property="skip.style"/>
            <then>
                <property name="skip.sass" value="1"/>
                <property name="skip.capture" value="1"/>
                <property name="skip.slice" value="1"/>
            </then>
        </if>
        
        <antcall target="-after-init"/>
    </target>

    <!--
    Clean
    -->
    <target name="-before-clean"/>
    <target name="-after-clean"/>
    <target name="clean" depends="init"
            description="Removes all build output produced by the 'build' target">
        <antcall target="-before-clean"/>

        <delete dir="${build.dir}"/>

        <antcall target="-after-clean"/>
    </target>
    
    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-after-sass"/>
    <target name="sass" depends="init" if="package.sass.dir.exists" unless="skip.sass"
            description="Builds the SASS files using Compass">
        <antcall target="-before-sass"/>

        <antcall target="-before-js"/>

        <x-compass-compile
            dir="${build.dir}"
            trace="true"
            boring="true"
            force="true"
            sassdir="${build.dir}"
            cssdir="${build.dir}/resources"
            config="${build.all.ruby}"/>
        
        <x-compress-css srcfile="${build.all.css.debug}"
                        outfile="${build.all.css}"/>

        <x-compress-css srcfile="${build.all.rtl.css.debug}"
                        outfile="${build.all.rtl.css}"/>

        <antcall target="-after-sass"/>
    </target>
    
    <!--
    Inherit Resources
    -->
    <target name="-before-inherit-resources"/>
    <target name="-after-inherit-resources"/>
    <target name="inherit-resources" depends="init" if="package.base.names"
            description="Performs the resource folder inheritance from base theme(s)">
        <antcall target="-before-inherit-resources"/>
        
        <for list="${package.base.names}" param="base">
            <sequential>
                <echo>Merging resources from base package @{base}</echo>
                <local name="base.path"/>
                <local name="base.resource.path"/>
                <property name="base.path" location="../@{base}"/>
                <property name="base.resource.path" location="${base.path}/resources"/>
                <if>
                    <available file="${base.resource.path}" type="dir"/>
                    <then>
                        <copy todir="${build.dir}/resources/" overwrite="true">
                            <fileset dir="${base.resource.path}" includes="**/*"/>
                        </copy>
                    </then>
                </if>
            </sequential>
        </for>

        <antcall target="-after-inherit-resources"/>
    </target>
    
    <!--
    Slice
    -->
    <target name="-before-slice"/>
    <target name="-after-slice"/>
    <target name="slice" depends="init" if="package.resources.dir.exists" unless="skip.slice"
            description="Slices CSS3 theme to produce non-CSS3 images and sprites">
        <antcall target="-before-slice"/>

        <copy todir="${build.dir}/example" overwrite="true">
            <fileset dir="${package.dir}/sass/example" includes="**/*"/>
        </copy>

        <echo>Capture theme image to ${build.dir}/theme-capture.png</echo>
        <x-sencha-command dir="${package.dir}">
            theme
                capture
                    -page=${build.dir}/example/theme.html
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
        </x-sencha-command>

        <echo>Slicing theme images to ${build.resources.dir}</echo>
        <x-sencha-command dir="${package.dir}">
            fs
                slice
                    ${build.slice.options}
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
                    -out=${build.resources.dir}
        </x-sencha-command>

        <antcall target="-after-slice"/>
    </target>
    
    <!--
    Copy Resources
    -->
    <target name="-before-copy-resources"/>
    <target name="-after-copy-resources"/>
    <target name="copy-resources" depends="init" if="package.resources.dir.exists" unless="skip.slice"
            description="Copy theme resources to folder">
        <antcall target="-before-copy-resources"/>

        <copy todir="${build.dir}/resources" overwrite="true">
            <fileset dir="${package.resources.dir}" includes="**/*"/>
        </copy>

        <antcall target="-after-copy-resources"/>
    </target>
    
    <!--
    Build JS
    -->
    <target name="-before-js"/>
    <target name="-after-js"/>
    <target name="js" depends="init" unless="skip.js"
            description="Builds the JS files">
        <antcall target="-before-js"/>
        
        <!--run compile command to generate concatenated output files for js and scss-->
        <x-sencha-command dir="${package.dir}">
            compile
                include
                    -all
                and
                ${build.compile.sass.rtl}
                and
                exclude
                    -namespace=Ext.rtl
                and
                ${build.compile.sass}
                and
                union
                    -tag=package-${package.name}
                and
                concat
                    -out=${build.all.dev.js}
                and
            
            -options=debug:false
                concatenate
                    -output-file=${build.all.debug.js}
                and
            
            -options=debug:false
                # TODO: exclude Ext.cmd.derive
                # optimize
                #    -define-rewrite
                # and
            
                concatenate
                    +yui
                    -output-file=${build.all.js}
                and
            
                
        </x-sencha-command>

        <antcall target="-after-js"/>
    </target>
    
    <!--
    Build JS
    -->
    <target name="-before-pkg"/>
    <target name="-after-pkg"/>
    <target name="pkg" depends="init" unless="skip.pkg"
            description="Builds the PKG file">
        <local name="package.json.staging"/>

        <antcall target="-before-pkg"/>

        
        <property name="package.json.file"
                  location="${package.dir}/package.json"/>
        
        <property name="package.deploy.json.name"
                  value="package.deploy.json"/>
        
        <property name="package.json.staging" 
                  value="${package.dir}/${package.deploy.json.name}"/>
        
        <zip destfile="${pkg.build.dir}/${pkg.file.name}">
            <fileset
                dir="${package.dir}"
                includes="${pkg.includes}"
                excludes="${pkg.excludes}"/>
        </zip>

        <x-script-def name="x-update-package-json">
            <attribute name="from"/>
            <attribute name="to"/>
            <script src="${cmd.config.dir}/ant/JSON.js"/>
            <script src="${cmd.config.dir}/ant/ant-util.js"/>
            <![CDATA[
                var pkgFile = attributes.get("from"),
                    pkgTmpFile = attributes.get("to");
                    config = readJson(pkgFile);
                    
                delete config.local;
                writeJson(pkgTmpFile, config);
            ]]>
        </x-script-def>

        <x-update-package-json
            from="${package.json.file}"
            to="${package.json.staging}"/>

        <zip destfile="${pkg.build.dir}/${pkg.file.name}"
             update="true">
            <mappedresources>
                <fileset dir="${package.dir}" includes="${package.deploy.json.name}"/>
                <globmapper from="*" to="package.json"/>
            </mappedresources>
        </zip>

        <delete file="${package.json.staging}"/>

        <antcall target="-after-pkg"/>
    </target>

    <!--
    Build
    -->
    <target name="-before-build"/>
    <target name="-build"
            depends="js,inherit-resources,copy-resources,sass,slice,pkg"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the package"/>

    <!--
    environment setters
    -->

    <!--
    Helpful targets
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <target name=".help" depends="init"
            description="Provides help on the build script">
        <local name="-alltargets"/>
        <x-shell outputproperty="-alltargets">
            ant -f ${ant.file} -p
        </x-shell>

        <!--
        Remove the annoying "Default taret:.help" smashed on the end of the output.
        -->
        <script language="javascript">
            var s = project.getProperty("-alltargets"),
                n = s.indexOf('Default target:');
            //self.log("all=" + n);
            project.setProperty("-alltargets", s.substring(0, n));
        </script>

        <echo><![CDATA[${-alltargets}
This is the main build script for your package.

The following properties can be used to disable certain steps in the build
process.

 * skip.pkg         Do not build the PKG file
 * skip.sass        Do not build the SASS.
 * skip.js          Do not build the JS files.

For details about how these options affect your build, see

    ${basedir}/.sencha/package/build-impl.xml

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    sencha ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    sencha ant .props
        ]]></echo>
    </target>

</project>
