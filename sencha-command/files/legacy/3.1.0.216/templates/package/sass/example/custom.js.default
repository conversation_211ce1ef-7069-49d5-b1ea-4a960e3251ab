/*
 * This file is generated as a starting point by Sencha Cmd - it will not be replaced or
 * updated by "sencha package upgrade".
 * 
 * This file can be removed and the script tag in theme.html removed if this theme does
 * not need custom additional manifest or shortcut entries. These are documented in
 * ./packages/ext-theme-base/sass/example/render.js.
 */

//Ext.theme.addManifest();

//Ext.theme.addShortcuts();
