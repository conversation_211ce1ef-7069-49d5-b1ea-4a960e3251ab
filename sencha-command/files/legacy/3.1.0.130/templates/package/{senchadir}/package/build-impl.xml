<?xml version="1.0" encoding="utf-8"?>
<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project>
    <!--
    Init-Local
    -->
    <target name="-before-init-local"/>
    <target name="-after-init-local"/>
    <target name="init-local">
        <antcall target="-before-init-local"/>

        <property file="${basedir}/../../local.properties"/>

        <script language="javascript">
            var f = new java.io.File(project.getProperty("basedir"));
            var sub = ".sencha/workspace/sencha.cfg";

            for (var p = f; p; p = p.getParentFile()) {
                var t = new java.io.File(p, sub);
                if (t.exists()) {
                    // we found the workspace folder!

                    t = new java.io.File(p, "local.properties");
                    if (t.exists()) {
                        var loader = project.createTask("property");
                        loader.setFile(new java.io.File(t.getCanonicalPath()));
                        loader.execute();
                    }

                    break;
                }
            }
        </script>

        <antcall target="-after-init-local"/>
    </target>

    <!--
    Find Sencha Cmd
    -->
    <target name="find-cmd" unless="cmd.dir">
        <!--
        Run "sencha which" to find the Sencha Cmd basedir and get "cmd.dir" setup. We
        need to execute the command with curdir set properly for Cmd to pick up that we
        are running for an application.
        -->
        <exec executable="sencha" dir="${basedir}">
            <arg value="which"/><arg value="-o=$cmddir$"/>
        </exec>

        <!-- Now read the generated properties file and delete it -->
        <property file="$cmddir$"/>
        <delete file="$cmddir$"/>

        <echo>Using Sencha Cmd from ${cmd.dir}</echo>
    </target>

    <!--
    Init
    -->
    <target name="-before-init"/>
    <target name="-after-init"/>
    <target name="init" depends="init-local,find-cmd">
        <antcall target="-before-init"/>

        <taskdef resource="com/sencha/ant/antlib.xml" classpath="${cmd.dir}/sencha.jar"/>
        <x-sencha-init prefix=""/>
        
        <!-- default the build environment to production if it is unset by this point -->
        <property name="args.environment" value="production"/>
        
        <!-- initialize the build.dir property from the workspace config after we've loaded it -->
        <property name="build.dir"
                  location="${package.build.dir}"/>

        <property name="build.images.dir" 
                  location="${build.dir}/resources"/>

        <!--
        Options to pass to the "sencha fs slice" command.
        -->
        <property name="build.slice.options" value=""/>

        <property name="pkg.build.dir" location="${workspace.build.dir}/${package.name}"/>
        <property name="pkg.file.name" value="${package.name}.pkg"/>
        <property name="pkg.includes" value="**/*"/>
        <property name="pkg.excludes" value=""/>

        <!-- Some operations require sencha.jar in the Ant classpath -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
        
        <!--<x-verify-app-cmd-ver/>-->

        <x-script-def name="x-load-json-cfg">
            <script src="${cmd.dir}/ant/JSON.js"/>
            <script src="${cmd.dir}/ant/ant-util.js"/>
            importPackage(com.sencha.util);
            importPackage(java.io);
            
            var packageDir = project.getProperty("package.dir"),
                packageCfg = JSON.parse(
                    FileUtil.readUnicodeFile(new File(packageDir, "package.json")));

            project.setProperty("package.version", packageCfg.version);
        </x-script-def>
        
        <x-load-json-cfg/>

        <antcall target="-after-init"/>
    </target>

    <!--
    Clean
    -->
    <target name="-before-clean"/>
    <target name="-after-clean"/>
    <target name="clean" depends="init"
            description="Removes all build output produced by the 'build' target">
        <antcall target="-before-clean"/>

        <delete dir="${build.dir}"/>

        <antcall target="-after-clean"/>
    </target>
    
    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-after-sass"/>
    <target name="sass" depends="init" unless="skip.sass"
            description="Builds the SASS files using Compass">
        <antcall target="-before-sass"/>

        <antcall target="-before-js"/>

        <x-shell dir="${build.dir}">
            compass compile --boring --force --sass-dir=${build.dir} --css-dir=${build.dir}/resources --config ${package.dir}/sass/config.rb
        </x-shell>

        <property name="package.out.css.debug" 
                  value="${build.dir}/resources/${package.name}-all-debug.css"/>
        
        <property name="package.out.rtl.css.debug" 
                  value="${build.dir}/resources/${package.name}-all-rtl-debug.css"/>
        
        <property name="package.out.css" 
                  value="${build.dir}/resources/${package.name}-all.css"/>
        
        <property name="package.out.rtl.css" 
                  value="${build.dir}/resources/${package.name}-all-rtl.css"/>
        
        <x-compress-css srcfile="${package.out.css.debug}"
                        outfile="${package.out.css}"/>

        <x-compress-css srcfile="${package.out.rtl.css.debug}"
                        outfile="${package.out.rtl.css}"/>

        <antcall target="-after-sass"/>
    </target>
    
    <!--
    Inherit Resources
    -->
    <target name="-before-inherit-resources"/>
    <target name="-after-inherit-resources"/>
    <target name="inherit-resources" depends="init"
            description="Performs the resource folder inheritance from base theme(s)">
        <antcall target="-before-inherit-resources"/>

        <if>
            <isset property="package.base.names"/>
            <then>
                <for list="${package.base.names}" param="base">
                    <sequential>
                        <echo>Merging resources from base package @{base}</echo>
                        <local name="base.path"/>
                        <local name="base.resource.path"/>
                        <property name="base.path" location="../@{base}"/>
                        <property name="base.resource.path" location="${base.path}/resources"/>
                        <copy todir="${build.dir}/resources/" overwrite="true">
                            <fileset dir="${base.resource.path}" includes="**/*"/>
                        </copy>
                    </sequential>
                </for>
            </then>
        </if>

        <antcall target="-after-inherit-resources"/>
    </target>
    
    <!--
    Capture
    -->
    <target name="-before-capture"/>
    <target name="-after-capture"/>
    <target name="capture" depends="init" unless="skip.capture"
            description="Captures the CSS3 theme image to produce non-CSS3 images and sprites">
        <antcall target="-before-capture"/>

        <copy todir="${build.dir}/example" overwrite="true">
            <fileset dir="${package.dir}/sass/example" includes="**/*"/>
        </copy>

        <echo>Capture theme image to ${build.dir}/theme-capture.png</echo>
        <x-sencha-command>
            theme
                capture
                    -page=${build.dir}/example/theme.html
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
        </x-sencha-command>

        <antcall target="-after-capture"/>
    </target>
    
    <!--
    Slice
    -->
    <target name="-before-slice"/>
    <target name="-after-slice"/>
    <target name="slice" depends="init" unless="skip.slice"
            description="Slices CSS3 theme to produce non-CSS3 images and sprites">
        <antcall target="-before-slice"/>

        <echo>Slicing theme images to ${build.images.dir}</echo>
        <x-sencha-command>
            fs
                slice
                    ${build.slice.options}
                    -image=${build.dir}/theme-capture.png
                    -manifest=${build.dir}/theme-capture.json
                    -out=${build.images.dir}
        </x-sencha-command>

        <antcall target="-after-slice"/>
    </target>
    
    <!--
    Copy Resources
    -->
    <target name="-before-copy-resources"/>
    <target name="-after-copy-resources"/>
    <target name="copy-resources" depends="init" unless="skip.slice"
            description="Copy theme resources to folder">
        <antcall target="-before-copy-resources"/>

        <copy todir="${build.dir}/resources" overwrite="true">
            <fileset dir="${package.dir}/resources" includes="**/*"/>
        </copy>

        <antcall target="-after-copy-resources"/>
    </target>
    
    <!--
    Build JS
    -->
    <target name="-before-js"/>
    <target name="-after-js"/>
    <target name="js" depends="init" unless="skip.js"
            description="Builds the JS files">
        <antcall target="-before-js"/>

        <property name="package.out.js" 
                  value="${build.dir}/${package.name}.js"/>

        <property name="package.out.scss" 
                  value="${build.dir}/${package.name}-all-debug.scss"/>
        
        <property name="package.out.rtl.scss" 
                  value="${build.dir}/${package.name}-all-rtl-debug.scss"/>
        
        <!--run compile command to generate concatenated output files for js and scss-->
        <x-normalize-path path="${build.dir}/resources" property="image.search.path"/>
        <x-sencha-command>
            compile
                union
                   -not
                   -tag=packageSrc,packageOverrides
                and
                sass
                    -variable=$image-search-path:'${image.search.path}'
                    -sass-out=${package.out.rtl.scss}
                and
                exclude
                    -namespace=Ext.rtl
                and
                sass
                    -variable=$image-search-path:'${image.search.path}'
                    -sass-out=${package.out.scss}
                and
                union
                    -tag=packageSrc,packageOverrides
                and
                concat
                    -out=${package.out.js}
        </x-sencha-command>

        <antcall target="-after-js"/>
    </target>
    
    <!--
    Build JS
    -->
    <target name="-before-pkg"/>
    <target name="-after-pkg"/>
    <target name="pkg" depends="init" unless="skip.pkg"
            description="Builds the PKG file">
        <antcall target="-before-pkg"/>

        <zip destfile="${pkg.build.dir}/${pkg.file.name}">
            <fileset
                dir="${package.dir}"
                includes="${pkg.includes}"
                excludes="${pkg.excludes}"/>
        </zip>

        <antcall target="-after-pkg"/>
    </target>

    <!--
    Build
    -->
    <target name="-before-build"/>
    <target name="-build"
            depends="js,inherit-resources,copy-resources,sass,capture,slice,pkg"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the package"/>

    <!--
    environment setters
    -->

    <!--
    Helpful targets
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <target name=".help" depends="init"
            description="Provides help on the build script">
        <local name="-alltargets"/>
        <x-shell outputproperty="-alltargets">
            ant -f ${ant.file} -p
        </x-shell>

        <!--
        Remove the annoying "Default taret:.help" smashed on the end of the output.
        -->
        <script language="javascript">
            var s = project.getProperty("-alltargets"),
                n = s.indexOf('Default target:');
            //self.log("all=" + n);
            project.setProperty("-alltargets", s.substring(0, n));
        </script>

        <echo><![CDATA[${-alltargets}
This is the main build script for your package.

The following properties can be used to disable certain steps in the build
process.

 * skip.pkg         Do not build the PKG file
 * skip.sass        Do not build the SASS.
 * skip.js          Do not build the JS files.

For details about how these options affect your build, see

    ${basedir}/.sencha/package/build-impl.xml

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    sencha ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    sencha ant .props
        ]]></echo>
    </target>

</project>
