<project basedir=".">
    <!--
    This file can be freely edited, so long as the <import file="plugin-impl.xml"/>
    statement is not removed.

    One of the purposes of this file is to hook various Sencha Command operations and do
    processing before or after the command is processed. To do this, simply provide the
    logic in a <target> using one of these names:

        -before-generate-app            Called before an application is generated
        -after-generate-app             Called after an application is generated

        -before-generate-controller     Called before a controller is generated
        -after-generate-controller      Called after a controller is generated

        -before-generate-model          Called before a model is generated
        -after-generate-model           Called after a model is generated

        -before-generate-profile        Called before a profile is generated
        -after-generate-profile         Called after a profile is generated
    -->
    <import file="${workspace.config.dir}/plugin.xml"/>

    <!--
    <target name="-after-generate-model">
        ... use ${args.path}, ${args.name} and ${args.fields} as needed ...
    </target>

    Other targets are similar. There are properties prefixed with "args." and the name of
    the command line option that hold the parameters for the command.
    -->
</project>