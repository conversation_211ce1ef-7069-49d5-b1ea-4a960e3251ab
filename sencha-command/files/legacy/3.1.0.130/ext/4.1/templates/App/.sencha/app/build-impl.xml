<?xml version="1.0" encoding="utf-8"?>
<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project>
    <!--
    Init-Local
    -->
    <target name="-before-init-local"/>
    <target name="-init-local">
        <property file="${basedir}/../../local.properties"/>

        <script language="javascript">
            var f = new java.io.File(project.getProperty("basedir"));
            var sub = ".sencha/workspace/sencha.cfg";

            for (var p = f; p; p = p.getParentFile()) {
                var t = new java.io.File(p, sub);
                if (t.exists()) {
                    // we found the workspace folder!

                    t = new java.io.File(p, "local.properties");
                    if (t.exists()) {
                        var loader = project.createTask("property");
                        loader.setFile(new java.io.File(t.getCanonicalPath()));
                        loader.execute();
                    }

                    break;
                }
            }
        </script>
    </target>
    <target name="-after-init-local"/>
    <target name="init-local"
            depends="-before-init-local,-init-local,-after-init-local"/>

    <!--
    Find Sencha Cmd
    -->
    <target name="find-cmd" unless="cmd.dir">
        <!--
        Run "sencha which" to find the Sencha Cmd basedir and get "cmd.dir" setup. We
        need to execute the command with curdir set properly for Cmd to pick up that we
        are running for an application.
        -->
        <exec executable="sencha" dir="${basedir}">
            <arg value="which"/><arg value="-o=$cmddir$"/>
        </exec>

        <!-- Now read the generated properties file and delete it -->
        <property file="$cmddir$"/>
        <delete file="$cmddir$"/>

        <echo>Using Sencha Cmd from ${cmd.dir}</echo>
    </target>

    <!--
    Init
    -->
    <target name="-before-init"/>
    <target name="-init">
        <taskdef resource="com/sencha/ant/antlib.xml" classpath="${cmd.dir}/sencha.jar"/>
        <x-sencha-init prefix=""/>
        
        <!-- default the build environment to production if it is unset by this point -->
        <property name="args.environment" value="production"/>
        
        <!-- initialize the build.dir property from the workspace config after we've loaded it -->
        <property name="build.dir"
                  location="${app.build.dir}/${args.environment}"/>
        
        <property name="build.compression.production" value="-yui"/>
        <property name="build.compression.testing" value=""/>
        
        <!--
            These properties can be modified to change general build options
            such as excluding files from the set.  The format expects newlines
            for each argument, for example:
            
            build.operations.production=exclude\n \
                                        -namespace\n \
                                        Ext
        -->
        <property name="build.operations.production" value=""/>
        <property name="build.operations.testing" value=""/>
        
        <property name="build.options.production" value="-debug=false"/>
        <property name="build.options.testing" value=""/>
        
        <!--
            This property can be modified to change the input and output page file 
            used in the compile command. (eg: index.aspx, index.jsp ... )
        -->
        <property name="app.page.name" value="index.html"/>
        
        <!-- Specify the input page file -->
        <property name="app.page.file" location="${app.dir}/${app.page.name}"/>
        
        <!-- Specify the output page file -->
        <property name="build.page.file" location="${build.dir}/${app.page.name}"/>
        
        <!-- Specify the output js file -->
        <property name="build.classes.name" value="all-classes.js"/>
        <property name="build.classes.file" location="${build.dir}/${build.classes.name}"/>
        
        <!-- init environment -->
        <property name="args.environment" value="production"/>
        
        <if>
            <equals arg1="${args.environment}" arg2="production"/>
            <then>
                <property name="build.compression" value="${build.compression.production}"/>
                <property name="build.operations" value="${build.operations.production}"/>
                <property name="build.options" value="${build.options.production}"/>
            </then>
            <else>
                <if>
                    <equals arg1="${args.environment}" arg2="testing"/>
                    <then>
                        <property name="build.compression" value="${build.compression.testing}"/>
                        <property name="build.operations" value="${build.operations.testing}"/>
                        <property name="build.options" value="${build.options.testing}"/>
                    </then>
                    <else>
                        <fail>The ExtJS SDK currently supports 'production' and 'testing' builds not ${args.environment}</fail>
                    </else>
                </if>
            </else>
        </if>
        
        <!-- Specify the name for the individual resource dirs in the app -->
        <property name="app.sass.name" value="sass"/>
        <property name="app.css.name" value="css"/>
        <property name="app.theme.name" value="theme"/>
        <property name="app.img.name" value="images"/>
        
        <!-- Specify the resources path in the app -->
        <property name="app.resources.dir" location="${app.dir}/resources"/>
        
        <!-- Specify the theme path in the app -->
        <property name="app.theme.dir" location="${app.resources.dir}/theme"/>
        
        <!-- Specify the sass path in the app -->
        <property name="app.sass.dir" location="${app.theme.dir}/${theme.name}/${app.sass.name}"/>
        
        <!-- Specify the resources path in the build -->
        <property name="build.resources.dir" location="${build.dir}/resources"/>
        
        <!-- Specify the files to exclude when copying resources to the build -->
        <property name="build.resources.exclude" 
                  value="${app.theme.name}/**/*"/>
       
        <!-- Specify the resources path in the app -->
        <property name="build.images.dir" location="${build.resources.dir}/images"/>
        
        <!-- Specify the sass path in the build -->
        <property name="build.css.dir" location="${build.resources.dir}/${app.sass.name}"/>
        
        <!-- The entry point for the theme command -->
        <property name="theme.page.name" value="theme.html"/>
        
        <!-- Some operations require sencha.jar in the Ant classpath -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
        
        <x-verify-app-cmd-ver/>
        
        <!--Base name for scss and css files-->
        <property name="app.out.base"
                  value="${app.name}-all"/>
        
        <!--Output scss file name-->
        <property name="app.out.scss" 
                  value="${build.dir}/${app.out.base}.scss"/>
        
        <!--output css relative path-->
        <property name="app.out.css.rel"
                  value="resources/${app.out.base}.css"/>
        
        <!--Output css file name-->
        <property name="app.out.css"
                  value="${build.dir}/${app.out.css.rel}"/>
        
        <!--sass-dir arg passed to compass cli-->
        <property name="compass.sass.dir"
                  value="${build.dir}"/>
        
        <!--css-dir arg passed to compass cli-->
        <property name="compass.css.dir"
                  value="${build.dir}/resources"/>
        
        <!--path to the compass config file-->
        <property name="compass.config.file"
                  value="${app.dir}/sass/config.rb"/>
        
        <!--default compass compile options-->
        <property name="compass.compile.options"
                  value="--trace --boring --force"/>
        
        <!--build command prefix that selects the app's transitive file set-->
        <property name="base.build.command">
            compile
                ${build.options}
                page
                    -name=page
                    -in=${app.page.file}
                    -out=${build.page.file}
                    -classes=${build.classes.name}
                    -stylesheet=${app.out.css.rel}
                and
                restore
                    page
        </property>
        
    </target>
    <target name="-after-init"/>
    <target name="init"
            depends="init-local,find-cmd,-before-init,-init,-after-init"/>

    <!--
    Clean
    -->
    <target name="-before-clean"/>
    <target name="-clean">
        <delete dir="${build.dir}"/>
    </target>
    <target name="-after-clean"/>
    <target name="clean"
            depends="init,-before-clean,-clean,-after-clean"
            description="Removes all build output produced by the 'build' target"/>

    
    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-sass">
        <x-normalize-path path="${build.dir}/resources" property="image.search.path"/>
        <x-sencha-command>
            ${base.build.command}
                and
                sass
                    -variable=$image-search-path:'${image.search.path}'
                    -sass-out=${app.out.scss}
        </x-sencha-command>
    </target>
    <target name="-after-sass"/>
    <target name="sass" depends="init" unless="skip.sass"
            description="Builds only the SASS files using Compass">
        <antcall target="-before-sass"/>
        <antcall target="-sass"/>
        <antcall target="-after-sass"/>
    </target>

    <!--
    Build Themes
    -->
    <target name="-before-theme"/>
    <target name="-theme">
        <if>
            <isset property="theme.base.names"/>
            <then>
                <for list="${theme.base.names}" param="base">
                    <sequential>
                        <echo>Merging resources from base package @{base}</echo>
                        <local name="base.path"/>
                        <local name="base.resource.path"/>
                        <property name="base.path" location="${workspace.packages.dir}/@{base}"/>
                        <property name="base.resource.path" location="${base.path}/build/resources"/>
                        <copy todir="${build.dir}/resources/" overwrite="true">
                            <fileset 
                                dir="${base.resource.path}" 
                                includes="**/*"
                                excludes="${base}-all*.css"/>
                        </copy>
                    </sequential>
                </for>
            </then>
        </if>

        <if>
            <isset property="app.requires.names"/>
            <then>
                <for list="${app.requires.names}" param="base">
                    <sequential>
                        <echo>Copying resources from required package @{base}</echo>
                        <local name="base.path"/>
                        <local name="base.resource.path"/>
                        <property name="base.path" location="${workspace.packages.dir}/@{base}"/>
                        <property name="base.resource.path" location="${base.path}/build/resources"/>
                        <copy todir="${build.dir}/resources/${base}" overwrite="true">
                            <fileset 
                                dir="${base.resource.path}" 
                                includes="**/*"
                                excludes="${base}-all*.css"/>
                        </copy>
                    </sequential>
                </for>
            </then>
        </if>

        <replaceregexp 
            match="^css_path = \$css_path = .*?$"
            replace="css_path = $css_path = '${compass.css.path}'"
            flags="g"
            byline="true">
            <fileset dir="${app.dir}/sass" includes="config.rb"/>
        </replaceregexp>
        
        <x-shell dir="${build.dir}">
            compass compile ${compass.compile.options} --sass-dir ${compass.sass.dir} --css-dir ${compass.css.dir} --config ${compass.config.file}
        </x-shell>
        
        <copy todir="${build.dir}/resources" overwrite="true">
            <fileset 
                dir="${app.dir}/resources" 
                includes="**/*" 
                excludes="**/Readme.md"/>
        </copy>
        
        <!--
        app.out.css.path is relative to the app output index.html file
        -->
        <x-get-relative-path
            from="${app.dir}"
            to="${app.out.css}"
            property="app.out.css.path"
            />
        
<echo file="${app.dir}/bootstrap.css">
/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It will
 * redirect to the most recently built css file for the application to
 * support development time inspection of css output.
 */
@import '${app.out.css.path}';
</echo>
    </target>
    <target name="-after-theme"/>
    <target name="theme" depends="init" unless="skip.theme"
            description="Builds only the application's Theme(s)">
        <antcall target="-before-theme"/>
        <antcall target="-theme"/>
        <antcall target="-after-theme"/>
    </target>

    <!--
    Refresh Individual Theme
    -->
    <target name="-before-refresh-theme"/>
    <target name="-refresh-theme">
<echo>
The 'sencha theme build ${args.themeName}' is not longer applicable to Saencha
Cmd apps beyond extjs version 4.2. The new package mechanism is now used to 
build themes.  Please run "sencha package build" for the theme used by the 
application (currently '${app.theme}').
</echo>
    </target>
    <target name="-after-refresh-theme"/>
    <target name="refresh-theme" depends="init" unless="skip.theme"
            description="Builds only the application's Theme(s)">
        <antcall target="-before-refresh-theme"/>
        <antcall target="-refresh-theme"/>
        <antcall target="-after-refresh-theme"/>
    </target>

    <!--
    Build Page
    -->
    <target name="-before-page"/>
    <target name="-page">
        
        <!-- compile the page -->
        <x-sencha-command>
            ${base.build.command}
                and
                ${build.operations}
                and
                concat
                    ${build.compression}
                    -out=${build.classes.file}
        </x-sencha-command>
    </target>
    <target name="-after-page"/>
    <target name="page" depends="init" unless="skip.page"
            description="Builds only the application's HTML page">
        <antcall target="-before-page"/>
        <antcall target="-page"/>
        <antcall target="-after-page"/>
    </target>

    <!--
    Build
    -->
    <target name="-before-build"/>
    <target name="-build" depends="sass,theme,page"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the application"/>

    <!--
    environment setters
    -->

    <target name="production">
        <property name="args.environment" value="production"/>
    </target>

    <target name="testing">
        <property name="args.environment" value="testing"/>
    </target>

    <target name="native">
        <property name="args.environment" value="native"/>
    </target>

    <target name="package">
        <property name="args.environment" value="package"/>
    </target>
    
    <!--
    Helpful targets
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <target name=".help" depends="init"
            description="Provides help on the build script">
        <local name="-alltargets"/>
        <x-shell outputproperty="-alltargets">
            ant -f ${ant.file} -p
        </x-shell>

        <!--
        Remove the annoying "Default taret:.help" smashed on the end of the output.
        -->
        <script language="javascript">
            var s = project.getProperty("-alltargets"),
                n = s.indexOf('Default target:');
            //self.log("all=" + n);
            project.setProperty("-alltargets", s.substring(0, n));
        </script>

        <echo><![CDATA[${-alltargets}
This is the main build script for your application.

The following properties can be used to disable certain steps in the build
process.

 * skip.page        Do not build the HTML page.
 * skip.sass        Do not build the SASS.
 * skip.theme       Do not build the theme images.
            
The following properties can be used to modify the build process.
            
 * build.compression.production         Set the compression for a production
                                        build.
 * build.compression.testing            Set the compression for a test build.
                                        (defaults to none)
 * build.compression                    Set the compression for all builds.
                                        (defaults to
                                        build.compression.production
                                        or build.compression.testing)
            
 * build.operations.production          Insert commands into the compile command
                                        for a production build. 
 * build.operations.testing             Insert commands into the compile command
                                        for a testing build.
 * build.operations                     Insert commands into the compile command
                                        for all builds.
                                        (defaults to build.operations.production
                                        or build.operations.testing)
 
 * build.options.production             Set options for a production build.
                                        (eg: -debug=false)
 * build.options.testing                Set options for a testing build.
 * build.options                        Set options for all builds.
                                        (defaults to build.options.production
                                        or build.options.testing)
 
 * app.page.name                        Set the input and output page file
                                        for the compile command.
 
 * build.classes.name                   Specify the compiled js file
                                        

For details about how these options affect your build, see

    ${basedir}/.sencha/app/build-impl.xml

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    ant .props
        ]]></echo>
    </target>

</project>
