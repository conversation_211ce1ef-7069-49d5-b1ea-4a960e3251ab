<?xml version="1.0" encoding="utf-8"?>
<project>
    <!--
    Init-Local
    -->
    <target name="-before-init-local"/>
    <target name="-init-local">
        <property file="${basedir}/../../local.properties"/>

        <script language="javascript">
            var f = new java.io.File(project.getProperty("basedir"));
            var sub = ".sencha/workspace/sencha.cfg";

            for (var p = f; p; p = p.getParentFile()) {
                var t = new java.io.File(p, sub);
                if (t.exists()) {
                    // we found the workspace folder!

                    t = new java.io.File(p, "local.properties");
                    if (t.exists()) {
                        var loader = project.createTask("property");
                        loader.setFile(new java.io.File(t.getCanonicalPath()));
                        loader.execute();
                    }

                    break;
                }
            }
        </script>
    </target>
    <target name="-after-init-local"/>
    <target name="init-local"
            depends="-before-init-local,-init-local,-after-init-local"/>

    <!--
    Find Sencha Cmd
    -->
    <target name="find-cmd" unless="cmd.dir">
        <!--
        Run "sencha which" to find the Sencha Cmd basedir and get "cmd.dir" setup. We
        need to execute the command with curdir set properly for Cmd to pick up that we
        are running for an application.
        -->
        <exec executable="sencha" dir="${basedir}">
            <arg value="which"/><arg value="-o=$cmddir$"/>
        </exec>

        <!-- Now read the generated properties file and delete it -->
        <property file="$cmddir$"/>
        <delete file="$cmddir$"/>

        <echo>Using Sencha Cmd from ${cmd.dir}</echo>
    </target>

    <!--
    Init
    -->
    <target name="-before-init"/>
    <target name="-init">
        <taskdef resource="com/sencha/ant/antlib.xml" classpath="${cmd.dir}/sencha.jar"/>
        <x-sencha-init prefix=""/>

        <!-- initialize the build.dir property from the workspace config after we've loaded it -->
        <property name="build.dir" location="${workspace.build.dir}"/>

        <!-- Some operations require sencha.jar in the Ant classpath -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
    </target>
    <target name="-after-init"/>
    <target name="init"
            depends="init-local,find-cmd,-before-init,-init,-after-init"/>

    <!--
    Clean
    -->
    <target name="-before-clean"/>
    <target name="-clean">
        <delete dir="${build.dir}"/>
    </target>
    <target name="-after-clean"/>
    <target name="clean"
            depends="init,-before-clean,-clean,-after-clean"
            description="Removes all build output produced by the 'build' target"/>

    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-sass">
        <x-shell dir="${app.dir}/resources/sass">
            compass compile --boring --force
        </x-shell>
    </target>
    <target name="-after-sass"/>
    <target name="sass" depends="init" unless="skip.sass"
            description="Builds only the SASS files using Compass">
        <antcall target="-before-sass"/>
        <antcall target="-sass"/>
        <antcall target="-after-sass"/>
    </target>
    
    <scriptdef name="x-app-build" language="javascript" src="${framework.config.dir}/app-build.js">
        <classpath>
            <pathelement location="${cmd.dir}/lib/ant-contrib-1.0b3.jar"/>
            <pathelement location="${cmd.dir}/sencha.jar"/>
        </classpath>
    </scriptdef>

    <!--
    Build Page
    -->
    <target name="-before-page"/>
    <target name="-page">
        <property name="v2deps" value="false"/>
        <property name="args.path" location="."/>
        <property name="args.destination" location="${workspace.build.dir}"/>
        <property name="args.environment" value="production"/>
        <x-app-build/>
    </target>
    <target name="-after-page"/>
    <target name="page" depends="init" unless="skip.page"
            description="Builds only the application's HTML page">
        <antcall target="-before-page"/>
        <antcall target="-page"/>
        <antcall target="-after-page"/>
    </target>

    <target name="-before-run"/>
    <target name="-run">
        <x-sencha-command>
            package
            run
            ${app.dir}/packager.temp.json
        </x-sencha-command>
    </target>
    <target name="-after-run"/>
    <target name="run" depends="init" unless="skip.run"
            description="Runs an application package using the native packager">
        
        <property name="args.autorun" value="false"/>
        <if>
            <and>
                <equals arg1="${args.autorun}" arg2="true"/>
                <or>
                    <equals arg1="${args.environment}" arg2="native"/>
                    <equals arg1="${args.environment}" arg2="package"/>
                </or>
            </and>
            <then>
                <antcall target="-before-run"/>
                <antcall target="-run"/>
                <antcall target="-after-run"/>
            </then>
        </if>
    </target>

    <!--
    Build
    -->
    <target name="-before-build"/>
    <target name="-build" depends="sass,page,run"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the application"/>

    <!--
    environment setters
    -->

    <target name="production">
        <property name="args.environment" value="production"/>
    </target>

    <target name="testing">
        <property name="args.environment" value="testing"/>
    </target>

    <target name="native">
        <property name="args.environment" value="native"/>
    </target>

    <target name="package">
        <property name="args.environment" value="package"/>
    </target>

    <!--
    Helpful targets
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <target name=".help" depends="init"
            description="Provides help on the build script">
        <local name="-alltargets"/>
        <x-shell outputproperty="-alltargets">
            ant -f ${ant.file} -p
        </x-shell>

        <!--
        Remove the annoying "Default taret:.help" smashed on the end of the output.
        -->
        <script language="javascript">
            var s = project.getProperty("-alltargets"),
                n = s.indexOf('Default target:');
            //self.log("all=" + n);
            project.setProperty("-alltargets", s.substring(0, n));
        </script>

        <echo><![CDATA[${-alltargets}
This is the main build script for your application.

The following properties can be used to disable certain steps in the build
process.

 * skip.page        Do not build the HTML page.
 * skip.sass        Do not build the SASS.
 * skip.theme       Do not build the theme images.

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    ant .props
        ]]></echo>
    </target>

</project>
