<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2012-2016. Sencha Inc.
  -->

<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project name="x-pkg-build-impl">
    
    <target name="init-cmd">
        <echo>Using Sencha Cmd from ${cmd.dir} for ${ant.file}</echo>

        <!--
        load the sencha.jar ant task definitions.
        
        NOTE: the 'loaderref' attribute stores this task def's class loader
        on the project by that name, so it will be sharable across sub-projects.
        This fixes out-of-memory issues, as well as increases performance.
        
        To supoprt this, it is recommended that any customizations that use
        'ant' or 'antcall' tasks set 'inheritrefs=true' on those tasks, in order
        to propagate the senchaloader reference to those subprojects.  
        
        The sencha 'x-ant-call' task, which extends 'antcall' and defaults 
        'inheritrefs' to true, may be used in place of antcall in 
        build process customizations.
        -->
        <taskdef resource="com/sencha/ant/antlib.xml" 
                 classpath="${cmd.dir}/sencha.jar"
                 loaderref="senchaloader"/>
        
        <!-- 
        Some operations require sencha.jar in the current java classpath, 
        so this will extend the java.lang.Thread#contextClassLoader with the 
        specified java classpath entries
        -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
    </target>

    <!--
    ===============================================================
    Init
    uses targets from init-impl.xml to load Sencha Cmd config
    system properties and ant task definitions
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/package/refresh-impl.xml"/>
    <import file="${cmd.dir}/ant/build/package/init-impl.xml"/>
    <target name="init"
            depends="init-local,
                     init-cmd,
                     -before-init,
                     -init,
                     -after-init,
                     -before-init-defaults,
                     -init-defaults,
                     -after-init-defaults,
                     -init-compiler"/>

    <!--
    ===============================================================
    Build
    this is the starting point for the build process. The 'depends'
    attribute on the -build target controls the ordering of the 
    different build phases
    ===============================================================
    -->
    <target name="-before-build"/>
    <target name="-build"
            depends="js,
                     deps,
                     inherit-resources,
                     copy-resources,
                     slice,
                     sass,
                     subpkgs,
                     examples,
                     pkg"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the package"/>

    <!--
    ===============================================================
    Clean
    removes all artifacts from the output build directories
    ===============================================================
    -->
    <target name="-before-clean"/>
    <target name="-after-clean"/>
    <target name="-clean">
        <delete dir="${package.output.base}"/>
        <delete dir="${build.temp.dir}"/>
    </target>
    <target name="clean" 
            depends="init"
            description="Removes all build output produced by the 'build' target">
        <x-ant-call>
            <target name="-before-clean"/>
            <target name="-clean"/>
            <target name="clean-subpkgs"/>
            <target name="clean-examples"/>
            <target name="-after-clean"/>
        </x-ant-call>
    </target>
    
    <!--
    ===============================================================
    JS
    uses targets from js-impl.xml to produce the output js files
    containing package js classes
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/package/js-impl.xml"/>
    <target name="js" 
            depends="init"
            description="Builds the JS files">
        <x-ant-call unless="skip.js">
            <target name="-before-js"/>
            <target name="-js"/>
            <target name="-after-js"/>
        </x-ant-call>
    </target>


    
    <target name="-before-deps"/>
    <target name="-deps">
        <property name="package.deps.file" value="${build.temp.dir}/deps.json"/>
        <property name="package.deps.base" value="${package.dir}"/>
        <x-compile refid="${compiler.ref.id}">
            <![CDATA[
                save
                    current
                and
                exclude
                    -all
                and
                include
                    -tag=package-${package.name}
                and
                meta
                    --info-type=RequiredFiles
                    -output=${package.deps.file}
                    -tpl={0}
                and
                restore
                    current
            ]]>        
        </x-compile>
    </target>
    <target name="-after-deps"/>

    <!--
    ===============================================================
    DEPS
    generates a json file listing all immediate js file dependencies
    of the js sources in this package.  Used by app builds to 
    filter out framework files.
    ===============================================================
    -->
    <target name="deps"
            depends="init"
            if="enable.deps.file"
            description="Builds the JS dependency file">
        <x-ant-call unless="skip.js">
            <target name="-before-deps"/>
            <target name="-deps"/>
            <target name="-after-deps"/>
        </x-ant-call>
    </target>



    <!--
    ===============================================================
    Resources
    uses targets from resources-impl.xml the package's resources 
    to the output directory
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/package/resources-impl.xml"/>

    <target name="inherit-resources" 
            depends="init" 
            description="Performs the resource folder inheritance from base theme(s)">
        <x-ant-call unless="skip.inherit">
            <target name="-before-inherit-resources"/>
            <target name="-inherit-resources"/>
            <target name="-after-inherit-resources"/>
        </x-ant-call>
    </target>
    
    <target name="copy-resources" 
            depends="init" 
            description="Copy theme resources to folder">
        <x-ant-call unless="skip.resources">
            <target name="-before-copy-resources"/>
            <target name="-copy-resources"/>
            <target name="-after-copy-resources"/>
        </x-ant-call>
    </target>
    
    <!--
    ===============================================================
    Sass
    uses targets from sass-impl.xml to produce the output css
    files for the package's styling
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/package/sass-impl.xml"/>
    <target name="sass" 
            depends="init"
            description="Builds the SASS files using Compass">
        <x-ant-call unless="skip.sass">
            <target name="-before-sass"/>
            <target name="-sass"/>
            <target name="-after-sass"/>
        </x-ant-call>
    </target>
    
    <!--
    ===============================================================
    Slice
    uses targets from slice-impl.xml to extract theme images from
    the package's styling for use with older browsers that don't 
    support modern css features
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/package/slice-impl.xml"/>
    <target name="slice" 
            depends="init" 
            description="Slices CSS3 theme to produce non-CSS3 images and sprites">
        <x-ant-call unless="skip.slice">
            <target name="-before-slice"/>
            <target name="-slice"/>
            <target name="-after-slice"/>
        </x-ant-call>
    </target>


    <!--
    ===============================================================
    Sub Builds
    uses targets from sub-builds.xml to process other packages
    and example applications contained within this package.
    Only the "subpkgs" and "examples" targets are part of the 
    main build sequence.
    ===============================================================
    -->
    <import file="sub-builds.xml"/>
    
    <!--
    sub packages
    -->
    <target name="subpkgs" 
            depends="init"
            description="Builds all sub-packages">
        <x-ant-call unless="skip.subpkgs">
            <target name="-before-subpkgs"/>
            <target name="-subpkgs"/>
            <target name="-after-subpkgs"/>
        </x-ant-call>
    </target>

    <target name="clean-subpkgs" 
            depends="init"
            description="Cleans all sub-packages">
        <x-ant-call unless="skip.clean.subpkgs">
            <target name="-before-clean-subpkgs"/>
            <target name="-clean-subpkgs"/>
            <target name="-after-clean-subpkgs"/>
        </x-ant-call>
    </target>

    <target name="upgrade-subpkgs" 
            depends="init"
            description="Upgrades all sub-packages">
        <x-ant-call unless="skip.upgrade.subpkgs">
            <target name="-before-upgrade-subpkgs"/>
            <target name="-upgrade-subpkgs"/>
            <target name="-after-upgrade-subpkgs"/>
        </x-ant-call>
    </target>
                
    <!--
    example applications
    -->
    <target name="examples" 
            depends="init"
            description="Builds all examples">
        <x-ant-call unless="skip.examples">
            <target name="-before-examples"/>
            <target name="-examples"/>
            <target name="-after-examples"/>
        </x-ant-call>
    </target>

    <target name="clean-examples" 
            depends="init"
            description="Upgrades all examples">
        <x-ant-call unless="skip.clean.examples">
            <target name="-before-clean-examples"/>
            <target name="-clean-examples"/>
            <target name="-after-clean-examples"/>
        </x-ant-call>
    </target>

    <target name="upgrade-examples" 
            depends="init"
            description="Upgrades all examples">
        <x-ant-call unless="skip.upgrade.examples">
            <target name="-before-upgrade-examples"/>
            <target name="-upgrade-examples"/>
            <target name="-after-upgrade-examples"/>
        </x-ant-call>
    </target>

    <!--
    Build PKG
    -->
    <target name="-before-pkg"/>
    <target name="-after-pkg"/>
    <target name="-pkg">
        <x-make-pkg dir="${package.dir}"
                    files="${build.pkg.manifest}"
                    pkg="${pkg.build.dir}/${pkg.file.name}"
                    temp="${build.temp.dir}"/>
    </target>
    <target name="pkg" 
            depends="init"
            description="Builds the PKG file">
        <x-ant-call  unless="skip.pkg">
            <target name="-before-pkg"/>
            <target name="-pkg"/>
            <target name="-after-pkg"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Build Dependencies
    uses the compiler to build metadata files for all detected
    file-to-file dependencies
    ===============================================================
    -->
    <target name="build-dependencies" depends="init,-init-compile-js">
        <x-compile refid="${compiler.ref.id}">
            <![CDATA[
            include
                -all
            and
            meta
                -infoType=Dependencies
                -basePath=${build.dir}
                -tpl={0}
                -out=${build.dir}/dependencies.json
            and
            meta
                -infoType=AppManifest
                -basePath=${build.dir}
                -tpl={0}
                -out=${build.dir}/bootsequence.json
            ]]>
        </x-compile>
    </target>

    <!--
    ===============================================================
    Help - properties
    displays all current ant properties
    ===============================================================
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <!--
    ===============================================================
    Help - docs
    displays the help message
    ===============================================================
    -->
    <target name=".help" depends="init"
            description="Provides help on the build script">

        <x-get-project-targets property="help.message"/>

        <echo><![CDATA[${help.message}
This is the main build script for your package.

The following properties can be used to disable certain steps in the build
process.

 * skip.pkg         Do not build the PKG file
 * skip.sass        Do not build the SASS.
 * skip.js          Do not build the JS files.

For details about how these options affect your build, see

    ${cmd.dir}/ant/build/package/build-impl.xml

These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace.

Alternatively, these can be supplied on the command line. For example:

    sencha ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    sencha ant .props
        ]]></echo>
    </target>

</project>
