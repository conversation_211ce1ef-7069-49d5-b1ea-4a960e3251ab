<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2012-2016. Sencha Inc.
  -->

<!--
********************************** DO NOT EDIT **********************************

This file will be replaced during upgrades so DO NOT EDIT this file. If you need to
adjust the process, reading and understanding this file is the first step.

In most cases, the adjustments can be achieved by setting properties or providing one
of the "hooks" in the form of a "-before-" or "-after-" target. Whenever possible, look
for one of these solutions.

Failing that, you can copy whole targets to your build.xml file and it will overrride
the target provided here. Doing that can create problems for upgrading to newer
versions of Cmd so it is not recommended but it will be easier to manage than editing
this file in most cases.
-->
<project name="x-app-build-impl" default=".help">
    <!--
    ===============================================================
    helper targets for ant integrations with IDE's 
    (human readable target names)
    ===============================================================
    -->
    <target name="-before-build-testing"/>
    <target name="-build-testing" depends="testing,build"/>
    <target name="-after-build-testing"/>
    <target name="build-testing" 
            depends="-before-build-testing,
                     -build-testing,
                     -after-build-testing"/>
    
    <target name="Build - Testing" 
            description="Create a Testing build of this project" 
            depends="build-testing"/>

    <target name="-before-build-production"/>
    <target name="-build-production" depends="production,build"/>
    <target name="-after-build-production"/>
    <target name="build-production" 
            depends="-before-build-production,
                     -build-production,
                     -after-build-production"/>
    
    <target name="Build - Production" 
            description="Create a Production build of this project" 
            depends="build-production"/>

    <target name="-before-build-native"/>
    <target name="-build-native" depends="native,build"/>
    <target name="-after-build-native"/>
    <target name="build-native"
            depends="-before-build-native,
                     -build-native,
                     -after-build-native"/>

    <target name="Build - Native" 
            description="Create a Native build of this project" 
            depends="build-native"/>
    
    <target name="-before-start-local-webserver"/>
    <target name="-start-local-webserver" depends="init">
        <x-launch-terminal>
            <![CDATA[
            ${cmd.dir}/sencha fs web -port=${build.web.port} start -map=${build.web.root}
            ]]>
        </x-launch-terminal>
    </target>
    <target name="-after-start-local-webserver"/>
    <target name="start-local-webserver" 
            depends="-before-start-local-webserver,
                     -start-local-webserver,
                     -after-start-local-webserver"/>

    <target name="WebServer - Start Local" 
            description="Starts a local webserver for this project" 
            depends="start-local-webserver"/>

    <target name="-before-compass-watch"/>
    <target name="-compass-watch" depends="init">
        <x-launch-terminal>
            compass watch ${app.sass.dir}
        </x-launch-terminal>
    </target>
    <target name="-after-compass-watch"/>
    <target name="compass-watch" 
            depends="-before-compass-watch,
                     -compass-watch,
                     -after-compass-watch"/>

    <target name="Compass - Watch" 
            description="Opens terminal and watches for SASS updates" 
            depends="compass-watch"/>
    
    <!--
    ===============================================================
    environment setters
    ===============================================================
    -->
    <target name="production" 
            description="Sets the build environment to production.">
        <property name="build.environment" value="production"/>
    </target>

    <target name="testing"
            description="Sets the build environment to testing.">
        <property name="build.environment" value="testing"/>
    </target>

    <target name="native"
            description="Sets the build environment to native.">
        <property name="build.environment" value="native"/>
    </target>

    <target name="package"
            description="Sets the build environment to package.">
        <property name="build.environment" value="package"/>
    </target>

    <target name="development"
            description="Sets the build environment to development.">
        <property name="build.environment" value="development"/>
    </target>

    <!--
        ===============================================================
        Find Cmd
        uses targets from find-cmd-impl.xml to detect the current 
        install of Sencha Cmd
        ===============================================================
        -->
    <target name="init-cmd">
        <echo>Using Sencha Cmd from ${cmd.dir} for ${ant.file}</echo>

        <!--
        load the sencha.jar ant task definitions.
        
        NOTE: the 'loaderref' attribute stores this task def's class loader
        on the project by that name, so it will be sharable across sub-projects.
        This fixes out-of-memory issues, as well as increases performance.
        
        To supoprt this, it is recommended that any customizations that use
        'ant' or 'antcall' tasks set 'inheritrefs=true' on those tasks, in order
        to propagate the senchaloader reference to those subprojects.  
        
        The sencha 'x-ant-call' task, which extends 'antcall' and defaults 
        'inheritrefs' to true, may be used in place of antcall in 
        build process customizations.
        -->
        <taskdef resource="com/sencha/ant/antlib.xml"
                 classpath="${cmd.dir}/sencha.jar"
                 loaderref="senchaloader"/>

        <!-- 
        Some operations require sencha.jar in the current java classpath, 
        so this will extend the java.lang.Thread#contextClassLoader with the 
        specified java classpath entries
        -->
        <x-extend-classpath>
            <jar path="${cmd.dir}/sencha.jar"/>
        </x-extend-classpath>
    </target>

    <!--
    ===============================================================
    Init
    uses targets from init-impl.xml to load Sencha Cmd config
    system properties and ant task definitions
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/init-impl.xml"/>
    <import file="${cmd.dir}/ant/build/app/resolve-impl.xml"/>
    <target name="init"
            depends="init-local,
                     init-cmd,
                     -before-init,
                     -init,
                     -after-init,
                     -before-init-defaults,
                     -init-defaults,
                     -after-init-defaults,
                     -init-compiler,
                     -init-native-package"/>
    
    <!--
    ===============================================================
    Build
    this is the starting point for the build process. The 'depends'
    attribute on the -build target controls the ordering of the 
    different build phases
    ===============================================================
    -->
    <target name="-before-build"/>
    <target name="-build" 
            depends="refresh,
                     resolve,
                     js,
                     resources,
                     slice,
                     sass,
                     page,
                     native-package"/>
    <target name="-after-build"/>
    <target name="build"
            depends="init,-before-build,-build,-after-build"
            description="Builds the application"/>

    <!--
    ===============================================================
    Clean
    removes all artifacts from the output build directories
    ===============================================================
    -->
    <target name="-before-clean"/>
    <target name="-clean">
        <delete dir="${app.output.base}"/>
        <delete dir="${build.temp.dir}"/>
    </target>
    <target name="-after-clean"/>
    <target name="clean"
            depends="init"
            description="Removes all build output produced by the 'build' target">
        <x-ant-call unless="skip.clean">
            <target name="-before-clean"/>
            <target name="-clean"/>
            <target name="-after-clean"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Watch
    uses targets from watch-impl.xml to initiate the application
    watch process using instrumented state from the compiler
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/watch-impl.xml"/>
    <target name="-watch-init">
        <property name="app.watch.enabled" value="true"/>
    </target>
    <target name="watch"
            depends="-watch-init,development,init"
            description="Starts Watch to keep your app ready for dev mode">
        <local name="watch.lock.file"/>
        <condition property="watch.lock.file" value="${build.id}.watch.lock">
            <isset property="build.id"/>
        </condition>
        <property name="watch.lock.file" value="app.watch.lock"/>

        <x-ant-call>
            <param name="build.id" value="${build.id}"/>
            <param name="build.name" value="${build.name}"/>
            <param name="watch.lock.file" value="${build.dir}/${watch.lock.file}"/>
            <target name="-before-watch"/>
            <target name="-watch"/>
            <target name="-after-watch"/>
        </x-ant-call>
    </target>
    
    <!--
    ===============================================================
    JS
    uses targets from js-impl.xml to produce the output js files
    containing needed application and framework js classes
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/js-impl.xml"/>
    <target name="js"
            depends="init"
            description="Builds the output javascript file(s)">
        <x-ant-call unless="skip.js">
            <target name="-before-js"/>
            <target name="-js"/>
            <target name="-after-js"/>
        </x-ant-call>
    </target>
        
    <!--
    ===============================================================
    Sass
    uses targets from sass-impl.xml to produce the output css
    files for the application's styling
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/sass-impl.xml"/>
    <target name="sass" 
            depends="init" 
            description="Builds the Sass files using Compass.">
        <x-ant-call unless="skip.sass">
            <target name="-before-sass"/>
            <target name="-sass"/>
            <target name="-after-sass"/>
            <target name="refresh"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Resources
    uses targets from resources-impl.xml to copy resources from 
    the application and required packages to the output directory
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/resources-impl.xml"/>
    <target name="resources" 
            depends="init"
            description="Copy resources to build folder.">
        <x-ant-call unless="skip.resources">
            <target name="-before-resources"/>

            <!-- Legacy targets: -->
            <target name="-before-inherit-resources"/>
            <target name="-before-copy-resources"/>

            <target name="-resources"/>
           
            <!-- Legacy targets: -->
            <target name="-after-copy-resources"/>
            <target name="-after-inherit-resources"/>

            <target name="-after-resources"/>
        </x-ant-call>
    </target>
    
    
    <!--
    ===============================================================
    Slice
    uses targets from slice-impl.xml to extract theme images from
    the application for use with older browsers that don't 
    support modern css features
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/slice-impl.xml"/>
    <target name="slice" 
            depends="init" 
            description="Slices CSS3 theme to produce non-CSS3 images and sprites.">
        <x-ant-call unless="skip.slice">
            <target name="-before-slice"/>
            <target name="-slice"/>
            <target name="-after-slice"/>
        </x-ant-call>
    </target>

    <!--
    Theme - this is a legacy support target for extjs 4.1 apps. It redirects
    to the "slice" ant target
    -->
    <target name="theme" 
            depends="init" 
            description="Builds the application's theme(s) images using the slicer (Ext JS 4.1 only).">
        <x-ant-call unless="skip.theme">
            <target name="-before-theme"/>
            <target name="slice"/>
            <target name="-after-theme"/>
        </x-ant-call>
    </target>
                
    <!--
    Refresh Theme - uses targets from refresh-impl.xml to rebuild the current
    theme
    -->
    <target name="refresh-theme" 
            depends="init" 
            description="Rebuilds the currently enabled app theme (Ext JS 4.1 only).">
        <x-ant-call unless="skip.theme">
            <target name="-before-refresh-theme"/>
            <target name="-refresh-theme"/>
            <target name="-after-refresh-theme"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Refresh
    uses targets from refresh-impl.xml to generate bootstrapping
    information for the application
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/refresh-impl.xml"/>
    <target name="refresh"
            depends="init"
            description="Refreshes the application bootstrap data.">
        <x-ant-call unless="skip.refresh">
            <target name="-before-refresh"/>
            <target name="-refresh"/>
            <target name="-after-refresh"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Page
    uses targets from page-impl.xml to generate the output markup
    file and associated microloader / app manifest
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/page-impl.xml"/>
    <target name="page" 
            depends="init" 
            description="Builds the application's HTML page.">
        <x-ant-call unless="skip.page">
            <target name="-before-page"/>
            <target name="-page"/>
            <target name="-after-page"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Resolve
    uses targets from resolve-impl.xml to detect dynamic app 
    dependencies using phantomjs
    ===============================================================
    -->
    <target name="resolve"
            depends="init"
            description="Resolve application dependencies dynamically.">
        <x-ant-call unless="skip.resolve">
            <target name="-before-resolve"/>
            <target name="-resolve"/>
            <target name="-after-resolve"/>
        </x-ant-call>
    </target>

    <!--
    ===============================================================
    Native Package
    uses targets from packager-impl.xml to run native packager
    applications to produce stand-alone installable web apps from
    this built Cmd application
    ===============================================================
    -->
    <import file="${cmd.dir}/ant/build/app/packager-impl.xml"/>
    <import file="${cmd.dir}/ant/build/app/cordova-impl.xml"/>
    <import file="${cmd.dir}/ant/build/app/phonegap-impl.xml"/>
    <target name="native-package"
            depends="init"
            description="Builds native packages of the application">
        <x-ant-call unless="skip.native-package">
            <target name="-before-native-package"/>
            <target name="-native-package"/>
            <target name="-after-native-package"/>
        </x-ant-call>
    </target>


    <target name="-before-publish"/>
    <target name="-after-publish"/>
    <target name="-publish">
        <property name="app.manager.file" value="${app.output.base}"/>
        <x-sencha-command>
            <![CDATA[
            manager
                version
                    create
                        -id=${app.manager.id}
                        -name=${app.version}
                        -host=${app.manager.host}
                        -secret=${app.manager.secret}
                        -apiKey=${app.manager.apiKey}
                        -file=${app.manager.file}
            ]]>
        </x-sencha-command>
    </target>
    <target name="publish"
            depends="init,-before-publish,-publish,-after-publish"
            description="Publish app to Sencha Web Application Manager"/>

    <!--
    ===============================================================
    Build Dependencies
    uses the compiler to build metadata files for all detected
    file-to-file dependencies
    ===============================================================
    -->
    <target name="build-dependencies" depends="init, -detect-app-build-properties">
        <x-compile refid="${compiler.ref.id}">
            <![CDATA[
            restore
                page
            and
            meta
                -infoType=Dependencies
                -basePath=${build.dir}
                -tpl={0}
                -out=${build.dir}/dependencies.json
            and
            meta
                -infoType=AppManifest
                -basePath=${build.dir}
                -tpl={0}
                -out=${build.dir}/bootsequence.json
            ]]>
        </x-compile>
    </target>

    <!--
    ===============================================================
    Help - properties
    displays all current ant properties
    ===============================================================
    -->
    <target name=".props" depends="init"
            description="Lists all properties defined for the build">
        <echoproperties/>
    </target>

    <!--
    ===============================================================
    Help - docs
    displays the help message
    ===============================================================
    -->
    <target name=".help" depends="init"
            description="Provides help on the build script">
        <x-get-project-targets property="help.message"/>

        <echo><![CDATA[${help.message}
This is the main build script for your application.

The following properties can be used to disable certain steps in the build
process.

 * skip.page        Do not build the HTML page.
 * skip.js          Do not build the output js code file(s)
 * skip.resources   Do not copy resources to the build directory
 * skip.sass        Do not build the SASS.
 * skip.slice       Do not build the theme image slicer.
            
Most build options are controlled by the app manifest. For details see:
    
    ${basedir}/app.json

For more specific controls over the build process, see:

    ${cmd.dir}/ant/build/app/defaults.properties
            
For details about how these options affect your build, see

    ${cmd.dir}/ant/build/app/build-impl.xml
    
These options can be stored in a local.properties file in this folder or in the
local.properties file in the workspace. This file should not be included in 
source control.

Alternatively, these can be supplied on the command line. For example:

    ant -Dskip.sass=1 build

To see all currently defined properties, do this:

    ant .props
        ]]></echo>
    </target>

</project>
