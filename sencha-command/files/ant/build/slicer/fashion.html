<!DOCTYPE html>
<!--
  ~ Copyright (c) 2012-2016. Sencha Inc.
  -->
<html>
<head>
    <title>Ext JS Theme Harness</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

    <script type="text/javascript">
        (function(){
            var search = location.search.substr(1).split("&");
            for (var i = 0; i < search.length; i++) {
                var tmp = search[i].split("="),
                        key = tmp[0],
                        value = tmp[1];

                if (key == '_baseDir') {
                    var a = document.createElement("a");
                    a.href = value;
                    value = a.href;

                    if (value.charAt(value.length - 1) != '/') {
                        value += '/';
                    }

                    console.log("setting base href to : " + value);
                    var base = document.createElement('base'),
                            head = document.head || document.getElementsByTagName('head')[0];

                    base.href = value;
                    if (head.firstChild) {
                        head.insertBefore(base, head.firstChild);
                    }
                    else {
                        head.appendChild(base);
                    }

                    // reset the href attribute to cuase IE to pick up the change
                    base.href = base.href;
                }
            }
        })();
    </script>
    
    <!--
    Load all required links and scripts
    -->
    <script type="text/javascript" src="/~cmd/extensions/sencha-fashion/fashion/fashion.js"></script>
    <script type="text/javascript" src="/~cmd/extensions/sencha-fashion/sass-compiler.js"></script>
    <script type="text/javascript" src="bootstrap.js"></script>

    <style>
        .widget-container {
            margin: 10px;
            width: 400px;
            position: relative;
            overflow: visible;
        }

        .x-slicer-target,
        .x-slicer-target * {
            opacity: 1;
        }

        /* ensure body does not overlap the rounded corners of framed panel/window when slicing */
        .x-panel-body,
        .x-window-body {
            background: transparent;
        }
    </style>
</head>
    <body></body>
</html>
