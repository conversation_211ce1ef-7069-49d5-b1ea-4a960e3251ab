{"name": "ext-precache", "version": "1.0.0", "description": "Creates service workers for progressive web apps built with Ext JS.", "main": "ext-precache.js", "bin": {"ext-precache": "./cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/sencha/rigel.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"minimist": "^1.2.8", "sw-precache": "^4.1.0", "escape-string-regexp": "^1.0.5"}}