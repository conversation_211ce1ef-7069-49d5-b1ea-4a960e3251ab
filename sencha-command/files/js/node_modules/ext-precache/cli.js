#! /usr/bin/env node

'use strict';

const fs = require('fs');
const extPrecache = require('./ext-precache');

let { buildDir, config } = require('minimist')(process.argv.slice(2), {
    string: ['buildDir', 'config']
});

if (!buildDir || !config) {
    console.log(`
Usage
    $ ext-service-worker-builder
    
Options
  --buildDir The path to build dir where the service worker should be created
  --config The path to the json file containing the configuration for sw-precache
Example
    $ node create-service-worker --buildDir=build/production/MyApp --config=build/production/temp/sw-precache-config.json
    `);

    process.exit(0);
};

config = JSON.parse(fs.readFileSync(config, 'utf8'));

extPrecache({ buildDir, config })
    .then(sw => console.log(`created: ${sw}`))
    .catch(e => {
        console.log(`error creating ${buildSW}`, e);
        process.exit(1);
    });