annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
application.title=sencha-command-test
application.vendor=kkrohe
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
# This directory is removed when the project is cleaned:
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
# Only compile against the classpath explicitly listed here:
build.sysclasspath=ignore
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
# Uncomment to specify the preferred debugger connection transport:
#debug.transport=dt_socket
debug.classpath=${run.classpath}
debug.test.classpath=${run.test.classpath}
# Files in build.classes.dir which should be excluded from distribution jar
dist.archive.excludes=
# This directory is removed when the project is cleaned:
dist.dir=dist
dist.jar=${dist.dir}/sencha-test.jar
dist.javadoc.dir=${dist.dir}/javadoc
endorsed.classpath=
excludes=
includes=**
jar.compress=false
junit.classpath=../lib/junit-4.11.jar\:../lib/hamcrest-core-1.3.jar
javac.classpath=${ivy.classpath}\:${junit.classpath}\:${reference.sencha-command.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.deprecation=false
javac.processorpath=${javac.classpath}
javac.source=1.8
javac.target=1.8
javac.test.classpath=${javac.classpath}\:${junit.classpath}\:${build.classes.dir}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=false
platform.active=default_platform
project.sencha-command=../sencha-command
reference.sencha-command.jar=${project.sencha-command}/dist/SenchaCmd/sencha.jar
run.classpath=${javac.classpath}\:${build.classes.dir}
# Space-separated list of JVM arguments used when running the project.
# You may also define separate properties like run-sys-prop.name=value instead of -Dname=value.
# To set system properties for unit tests define test-sys-prop.name=value:
run.jvmargs=
run.test.classpath=${dist.dir}/../../sencha-command/dist/SenchaCmd/sencha.jar\:${junit.classpath}\:${dist.jar}\:${build.test.classes.dir}
source.encoding=UTF-8
src.dir=src
test.src.dir=test
junit.forkmode=once
libs.CopyLibs.classpath=../lib/org-netbeans-modules-java-j2seproject-copylibstask.jar

ivy.classpath=lib/ant-1.8.4.jar\:lib/ant-launcher-1.8.4.jar\:lib/bcpkix-jdk15on-1.48.jar\:lib/bcprov-jdk15on-1.48.jar\:lib/cglib-nodep-2.1_3.jar\:lib/commons-codec-1.17.0.jar\:lib/commons-collections4-4.5.0-M2-javadoc.jar\:lib/commons-collections4-4.5.0-M2-sources.jar\:lib/commons-collections4-4.5.0-M2.jar\:lib/commons-exec-1.1.jar\:lib/commons-io-2.18.0-javadoc.jar\:lib/commons-io-2.18.0-sources.jar\:lib/commons-io-2.18.0.jar\:lib/commons-jxpath-1.3.jar\:lib/commons-lang3-3.1.jar\:lib/commons-logging-1.1.3.jar\:lib/cssparser-0.9.11.jar\:lib/error_prone_annotations-2.27.0.jar\:lib/gson-2.11.0.jar\:lib/guava-15.0.jar\:lib/htmlunit-2.13.jar\:lib/htmlunit-core-js-2.13.jar\:lib/httpclient-4.3.1.jar\:lib/httpcore-4.3.jar\:lib/httpmime-4.3.1.jar\:lib/ini4j-0.5.2.jar\:lib/jakarta.servlet-api-6.0.0.jar\:lib/jcip-annotations-1.0.jar\:lib/jetty-alpn-client-12.0.15.jar\:lib/jetty-client-12.0.15.jar\:lib/jetty-ee-12.0.15.jar\:lib/jetty-ee10-servlet-12.0.15.jar\:lib/jetty-ee10-webapp-12.0.15.jar\:lib/jetty-http-12.0.15.jar\:lib/jetty-http-8.1.12.v20130726.jar\:lib/jetty-io-12.0.15.jar\:lib/jetty-io-8.1.12.v20130726.jar\:lib/jetty-proxy-12.0.15.jar\:lib/jetty-repacked-7.6.1.jar\:lib/jetty-security-12.0.15.jar\:lib/jetty-server-12.0.15.jar\:lib/jetty-session-12.0.15.jar\:lib/jetty-util-12.0.15.jar\:lib/jetty-util-8.1.12.v20130726.jar\:lib/jetty-websocket-8.1.12.v20130726.jar\:lib/jetty-xml-12.0.15.jar\:lib/jna-3.4.0.jar\:lib/json-20080701.jar\:lib/mx4j-tools-3.0.1.jar\:lib/nekohtml-1.9.19.jar\:lib/netty-3.5.2.Final.jar\:lib/operadriver-1.5.jar\:lib/operalaunchers-1.1.jar\:lib/platform-3.4.0.jar\:lib/protobuf-java-2.4.1.jar\:lib/sac-1.3.jar\:lib/selenium-android-driver-2.39.0.jar\:lib/selenium-api-2.39.0.jar\:lib/selenium-chrome-driver-2.39.0.jar\:lib/selenium-firefox-driver-2.39.0.jar\:lib/selenium-htmlunit-driver-2.39.0.jar\:lib/selenium-ie-driver-2.39.0.jar\:lib/selenium-iphone-driver-2.39.0.jar\:lib/selenium-java-2.39.0.jar\:lib/selenium-remote-driver-2.39.0.jar\:lib/selenium-safari-driver-2.39.0.jar\:lib/selenium-server-2.39.0.jar\:lib/selenium-support-2.39.0.jar\:lib/serializer-2.7.1.jar\:lib/servlet-api-2.5-6.1.9.jar\:lib/slf4j-api-1.6.6.jar\:lib/slf4j-api-2.0.16.jar\:lib/snakeyaml-1.8.jar\:lib/webbit-0.4.14.jar\:lib/xalan-2.7.1.jar\:lib/xercesImpl-2.11.0.jar\:lib/xml-apis-1.4.01.jar
