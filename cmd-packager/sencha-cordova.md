This command will be deprecated in a future version of CMD. Please see information on setting up a multi-build via app.json instead of running these commands.

Sencha Cmd works together with the Cordova CLI to package your application for native platforms.

    // Initialize Cordova Support 
    sencha cordova init {APP_ID} {APP_NAME}

    // Build the application and attempt to run it on a Device or in the Emulator
    sencha app build -run native

For more information on using Sencha Cmd with <PERSON>rdova, consult the guides found here:

http://docs.sencha.com/touch/2.3.0/#!/guide/cordova
