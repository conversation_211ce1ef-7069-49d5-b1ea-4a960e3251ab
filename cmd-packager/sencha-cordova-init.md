This will add Cordova CLI support to your application. Cordova CLI support will
allow for native application building locally for multiple platforms.  

    sencha cordova init {APP_ID} {APP_NAME}

{APP_ID} is optional and contains the Application ID used in the native app. This
will default to foo.bar.{APP_NAME}

{APP_NAME} is optional and contains the Application Name used in the native app. This
will default to the name of your sencha touch application.

For more information on using Sencha Cmd with <PERSON>rdo<PERSON>, consult the guides found here:

http://docs.sencha.com/touch/2.3.0/#!/guide/cordova