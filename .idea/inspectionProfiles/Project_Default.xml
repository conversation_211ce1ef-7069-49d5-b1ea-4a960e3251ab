<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="JSValidateJSON" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="myWarnAboutComments" value="false" />
    </inspection_tool>
    <inspection_tool class="LoggerInitializedWithForeignClass" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="loggerClassName" value="org.apache.log4j.Logger,org.slf4j.LoggerFactory,org.apache.commons.logging.LogFactory,java.util.logging.Logger" />
      <option name="loggerFactoryMethodName" value="getLogger,getLogger,getLog,getLogger" />
    </inspection_tool>
  </profile>
</component>