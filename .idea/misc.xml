<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CppTools.Loader" reportImplicitCastToBool="false" reportNameReferencedOnce="false" version="3" compilerSelect="AUTO" />
  <component name="FrameworkDetectionExcludesConfiguration">
    <type id="JRUBY" />
    <type id="sass-stdlib" />
  </component>
  <component name="IdProvider" IDEtalkID="87D8F84818A67B67DCFE4B6103C7D59A" />
  <component name="IvyIDEA.ProjectSettings">
    <option name="artifactTypeSettings">
      <ArtifactTypeSettings>
        <option name="classesTypes" value="jar, mar, sar, war, ear, ejb, bundle, test-jar" />
        <option name="javadocTypes" value="javadoc, doc, docs, apidoc, apidocs, documentation, documents" />
        <option name="sourcesTypes" value="source, src, sources, srcs" />
      </ArtifactTypeSettings>
    </option>
    <option name="ivySettingsFile" value="$PROJECT_DIR$/ivysettings.xml" />
    <option name="propertiesSettings">
      <PropertiesSettings />
    </option>
  </component>
  <component name="JavaScriptSettings">
    <option name="languageLevel" value="ES6" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/modules/doxi/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_7" default="false" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/.build" />
  </component>
</project>