<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SenchaCmd service test" type="JUnit" factoryName="JUnit" singleton="true">
    <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea">
      <pattern>
        <option name="PATTERN" value="com.sencha.tools.server.service.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <module name="sencha-command-service" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" value="" />
    <option name="PACKAGE_NAME" value="com.sencha.tools.server.service" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="directory" />
    <option name="VM_PARAMETERS" value="-ea" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="file://$MODULE_DIR$/build" />
    <option name="ENV_VARIABLES" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <envs />
    <dir value="$PROJECT_DIR$/sencha-command-service/test" />
    <patterns />
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method>
      <option name="Make" enabled="false" />
      <option name="AntTarget" enabled="true" antfile="file://$PROJECT_DIR$/sencha-command-service/build.xml" target="external-test-setup" />
    </method>
  </configuration>
</component>