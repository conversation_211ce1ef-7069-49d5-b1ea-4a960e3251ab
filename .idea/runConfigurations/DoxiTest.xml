<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="DoxiTest" type="JUnit" factoryName="JUnit" nameIsGenerated="true">
    <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea">
      <pattern>
        <option name="PATTERN" value="com.sencha.doxi.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <module name="sencha-command" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" />
    <option name="PACKAGE_NAME" value="com.sencha.doxi" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.doxi.DoxiTest" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="file://$MODULE_DIR$" />
    <option name="ENV_VARIABLES" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="singleModule" />
    </option>
    <envs />
    <patterns />
    <method>
      <option name="Make" enabled="false" />
      <option name="AntTarget" enabled="true" antfile="file://$PROJECT_DIR$/sencha-command/build.xml" target="external-test-setup" />
    </method>
  </configuration>
</component>