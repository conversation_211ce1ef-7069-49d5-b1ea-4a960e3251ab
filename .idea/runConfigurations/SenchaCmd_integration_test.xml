<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SenchaCmd integration test" type="JUnit" factoryName="JUnit" singleton="true">
    <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
    <module name="sencha-test-harness" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" value="" />
    <option name="PACKAGE_NAME" value="com.sencha" />
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="directory" />
    <option name="VM_PARAMETERS" value="-ea -Xms256m -Xmx1024m" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="file://$MODULE_DIR$/build/test-temp" />
    <option name="ENV_VARIABLES" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="singleModule" />
    </option>
    <envs />
    <dir value="$PROJECT_DIR$/sencha-test-harness/test" />
    <patterns />
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Run" />
    <method>
      <option name="Make" enabled="false" />
      <option name="AntTarget" enabled="true" antfile="file://$PROJECT_DIR$/sencha-test-harness/build.xml" target="external-test-setup" />
    </method>
  </configuration>
</component>