<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="AppTest.autoDepAppRefresh" type="JUnit" factoryName="JUnit" singleton="true" nameIsGenerated="true">
    <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea">
      <pattern>
        <option name="PATTERN" value="com.sencha.integration.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <module name="sencha-test-harness" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" value="" />
    <option name="PACKAGE_NAME" value="com.sencha.integration" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.integration.AppTest" />
    <option name="METHOD_NAME" value="autoDepAppRefresh" />
    <option name="TEST_OBJECT" value="method" />
    <option name="VM_PARAMETERS" value="-ea" />
    <option name="PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="file://$MODULE_DIR$/build/test-temp" />
    <option name="ENV_VARIABLES" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <envs />
    <patterns />
    <RunnerSettings RunnerId="Debug">
      <option name="DEBUG_PORT" value="" />
      <option name="TRANSPORT" value="0" />
      <option name="LOCAL" value="true" />
    </RunnerSettings>
    <RunnerSettings RunnerId="Run" />
    <ConfigurationWrapper RunnerId="Debug" />
    <ConfigurationWrapper RunnerId="Run" />
    <method>
      <option name="Make" enabled="false" />
      <option name="AntTarget" enabled="true" antfile="file://$PROJECT_DIR$/sencha-test-harness/build.xml" target="external-test-setup" />
    </method>
  </configuration>
</component>