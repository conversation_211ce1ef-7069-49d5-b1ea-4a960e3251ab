<component name="libraryTable">
  <library name="sencha-lib">
    <CLASSES>
      <root url="file://$PROJECT_DIR$/sencha-command/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="file://$PROJECT_DIR$/sencha-command/lib" />
      <root url="jar://$USER_HOME$/.ideaLibSources/rhino-1.7R4-sources.jar!/" />
      <root url="file://$PROJECT_DIR$/modules/doxi/src/main/java" />
      <root url="jar://$USER_HOME$/.ideaLibSources/yuicompressor-2.4.8-sources.jar!/" />
      <root url="jar://$USER_HOME$/.ideaLibSources/gson-2.7-sources.jar!/" />
      <root url="jar://$USER_HOME$/.ideaLibSources/closure-compiler-v20180610-sources.jar!/" />
      <root url="jar://$USER_HOME$/.ideaLibSources/guava-20.0-sources.jar!/" />
    </SOURCES>
    <jarDirectory url="file://$PROJECT_DIR$/sencha-command/lib" recursive="false" />
    <jarDirectory url="file://$PROJECT_DIR$/sencha-command/lib" recursive="false" type="SOURCES" />
  </library>
</component>