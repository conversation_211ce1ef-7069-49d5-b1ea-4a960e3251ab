<component name="libraryTable">
  <library name="sencha">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/sencha-command/dist/SenchaCmd/sencha.jar!/" />
      <root url="file://$PROJECT_DIR$/sencha-command/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="file://$PROJECT_DIR$/sencha-command/src" />
      <root url="file://$PROJECT_DIR$/sencha-command/lib" />
    </SOURCES>
    <jarDirectory url="file://$PROJECT_DIR$/sencha-command/lib" recursive="false" />
    <jarDirectory url="file://$PROJECT_DIR$/sencha-command/lib" recursive="false" type="SOURCES" />
  </library>
</component>